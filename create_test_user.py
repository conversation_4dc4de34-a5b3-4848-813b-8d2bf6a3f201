#!/usr/bin/env python3
"""
Create a test user via the API registration endpoint.
"""
import requests
import json

API_BASE_URL = "https://d26l8pqbmembp6.cloudfront.net/api/v1"

def create_test_user():
    """Create a test user via the registration API."""
    print("🔧 Creating test user via API registration...")
    
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/auth/register", json=user_data)
        
        if response.status_code == 201:
            user_info = response.json()
            print("✅ Test user created successfully!")
            print(f"   Email: {user_info['email']}")
            print(f"   Username: {user_info['username']}")
            print(f"   Full Name: {user_info['full_name']}")
            print(f"   User ID: {user_info['id']}")
            print(f"   Active: {user_info['is_active']}")
            print(f"   Verified: {user_info['is_verified']}")
            return True
        elif response.status_code == 400:
            error_detail = response.json().get('detail', 'Unknown error')
            if "already registered" in error_detail or "already taken" in error_detail:
                print("ℹ️  Test user already exists!")
                return test_login()
            else:
                print(f"❌ Registration failed: {error_detail}")
                return False
        else:
            print(f"❌ Registration failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        return False

def test_login():
    """Test login with the test user credentials."""
    print("\n🔐 Testing login with test user credentials...")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/auth/login", json=login_data)
        
        if response.status_code == 200:
            token_info = response.json()
            print("✅ Login successful!")
            print(f"   Access Token: {token_info['access_token'][:50]}...")
            print(f"   Token Type: {token_info['token_type']}")
            print(f"   Expires In: {token_info['expires_in']} seconds")
            print(f"   User: {token_info['user']['username']} ({token_info['user']['email']})")
            return True
        else:
            print(f"❌ Login failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return False

def main():
    print("🚀 Expendra Test User Setup")
    print("=" * 40)
    
    # Try to create user first
    if create_test_user():
        print("\n🎯 Test user is ready!")
        print("\nYou can now login with:")
        print("   Email: <EMAIL>")
        print("   Password: testpassword123")
    else:
        print("\n❌ Failed to set up test user")

if __name__ == "__main__":
    main()
