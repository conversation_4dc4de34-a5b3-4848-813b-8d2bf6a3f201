# Expendra CI/CD Pipeline

This repository contains a complete CI/CD pipeline for the Expendra AI-powered purchasing assistant application. The pipeline provides automated testing, building, and deployment to AWS using modern DevOps practices.

## 🏗️ Architecture Overview

The CI/CD pipeline consists of:

- **Frontend**: React/TypeScript application with Vite build system
- **Backend**: Python FastAPI application with AI orchestration
- **Database**: PostgreSQL on AWS RDS
- **Infrastructure**: AWS ECS, ALB, CloudFront, S3
- **Monitoring**: CloudWatch logs, metrics, and dashboards
- **Security**: Vulnerability scanning, secrets management

## 📁 Repository Structure

```
├── .github/workflows/          # GitHub Actions CI/CD workflows
│   ├── ci-cd.yml              # Main CI/CD pipeline
│   ├── security.yml           # Security scanning
│   └── dependabot.yml         # Dependency updates
├── backend/                   # Python FastAPI backend
│   ├── Dockerfile             # Backend container configuration
│   └── config/                # AWS and logging configuration
├── frontend/                  # React TypeScript frontend
│   ├── Dockerfile             # Frontend container configuration
│   └── nginx.conf             # Nginx configuration
├── infrastructure/            # Terraform infrastructure as code
│   ├── main.tf                # Main Terraform configuration
│   ├── modules/               # Reusable Terraform modules
│   └── terraform.tfvars.example
├── scripts/                   # Deployment and management scripts
│   ├── deploy.bat             # Master deployment script
│   ├── ci-deploy.sh           # CI/CD deployment script
│   ├── monitor.bat            # Monitoring and health checks
│   ├── manage-secrets.bat     # Secrets management
│   └── validate-deployment.sh # Deployment validation
├── config/                    # Environment configurations
│   └── environments.yml       # Environment-specific settings
└── docs/                      # Documentation
    └── DEPLOYMENT_TESTING.md   # Comprehensive testing guide
```

## 🚀 Quick Start

### 1. Prerequisites

- AWS CLI configured with appropriate permissions
- Docker and Docker Compose installed
- Terraform installed (version 1.0+)
- Node.js 18+ and Python 3.11+
- GitHub repository with Actions enabled

### 2. Configure GitHub Secrets

Add these secrets to your GitHub repository:

```
AWS_ACCESS_KEY_ID          # AWS access key
AWS_SECRET_ACCESS_KEY      # AWS secret key
AWS_REGION                 # AWS region (e.g., us-east-1)
```

Optional secrets for production:
```
OPENAI_API_KEY            # OpenAI API key
ANTHROPIC_API_KEY         # Anthropic API key
GOOGLE_API_KEY            # Google API key
DATABASE_PASSWORD         # Custom database password
SECRET_KEY                # Application secret key
```

### 3. Deploy Infrastructure

```bash
# Copy and customize Terraform variables
cd infrastructure
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your specific values

# Deploy infrastructure
./deploy.bat init
./deploy.bat apply
```

### 4. Deploy Application

```bash
# Deploy using the master script
./scripts/deploy.bat prod deploy

# Or trigger via GitHub Actions
git tag v1.0.0
git push origin v1.0.0
```

### 5. Validate Deployment

```bash
# Run comprehensive validation
./scripts/validate-deployment.sh

# Check application health
./scripts/monitor.bat health prod
```

## 🔄 CI/CD Workflows

### Main CI/CD Pipeline (`.github/workflows/ci-cd.yml`)

**Triggers:**
- Push to `main` branch
- Pull requests to `main`
- Manual workflow dispatch

**Stages:**
1. **Test**: Run unit tests, linting, and type checking
2. **Build**: Build Docker images and push to ECR
3. **Deploy**: Deploy to staging (PR) or production (main)
4. **Validate**: Run health checks and validation tests

### Security Workflow (`.github/workflows/security.yml`)

**Triggers:**
- Weekly schedule
- Manual workflow dispatch

**Scans:**
- Python dependencies (Safety, Bandit)
- Node.js dependencies (npm audit)
- Container vulnerabilities (Trivy)
- Code quality (CodeQL)

### Dependency Management (`.github/dependabot.yml`)

**Automated Updates:**
- Python packages (weekly)
- npm packages (weekly)
- Docker base images (weekly)
- GitHub Actions (weekly)

## 🏗️ Infrastructure Components

### AWS Services Used

- **ECS Fargate**: Container orchestration
- **Application Load Balancer**: Traffic routing
- **RDS PostgreSQL**: Database
- **ECR**: Container registry
- **S3**: Static asset storage
- **CloudFront**: CDN
- **Systems Manager**: Secrets management
- **CloudWatch**: Monitoring and logging
- **VPC**: Network isolation

### Terraform Modules

- **VPC**: Network infrastructure with public/private subnets
- **ECS**: Container services and task definitions
- **RDS**: Database with backup and monitoring
- **ALB**: Load balancer with SSL termination
- **S3**: Buckets for static assets and Terraform state
- **CloudFront**: CDN distribution
- **SSM**: Parameter store for secrets
- **Monitoring**: CloudWatch dashboards and alarms

## 🔧 Management Scripts

### Deployment Scripts

- `scripts/deploy.bat`: Master deployment script for Windows
- `scripts/ci-deploy.sh`: CI/CD deployment script for Linux
- `infrastructure/deploy.bat`: Terraform-specific deployment

### Monitoring Scripts

- `scripts/monitor.bat`: Health checks and monitoring
- `scripts/validate-deployment.sh`: Comprehensive validation

### Secrets Management

- `scripts/manage-secrets.bat`: SSM Parameter Store management

## 📊 Monitoring and Logging

### CloudWatch Integration

- **Structured Logging**: JSON logs with correlation IDs
- **Custom Metrics**: Application performance metrics
- **Dashboards**: Real-time monitoring dashboards
- **Alarms**: Automated alerting for issues

### Health Checks

- Application health endpoints
- Database connectivity checks
- Load balancer health checks
- Container health monitoring

## 🔒 Security Features

### Container Security

- Non-root user containers
- Multi-stage builds for minimal attack surface
- Regular vulnerability scanning
- Signed container images

### Secrets Management

- AWS Systems Manager Parameter Store
- No hardcoded credentials
- Environment-specific secret isolation
- Automatic secret rotation support

### Network Security

- VPC with private subnets
- Security groups with least privilege
- SSL/TLS encryption
- WAF protection (configurable)

## 🧪 Testing Strategy

### Automated Testing

- Unit tests for backend and frontend
- Integration tests for API endpoints
- Security vulnerability scanning
- Infrastructure validation tests

### Manual Testing

- End-to-end application testing
- Performance testing
- Security penetration testing
- Disaster recovery testing

## 📈 Scaling and Performance

### Auto Scaling

- ECS service auto scaling based on CPU/memory
- Application Load Balancer health checks
- Database connection pooling
- CDN caching for static assets

### Performance Optimization

- Container resource optimization
- Database query optimization
- Frontend code splitting
- Image optimization and compression

## 🔄 Deployment Strategies

### Blue-Green Deployment

- Zero-downtime deployments
- Automatic rollback on failure
- Health check validation
- Traffic shifting

### Environment Management

- Development, staging, and production environments
- Environment-specific configurations
- Isolated infrastructure per environment
- Promotion pipeline between environments

## 📚 Documentation

- [Deployment Testing Guide](docs/DEPLOYMENT_TESTING.md): Comprehensive testing procedures
- [Environment Configuration](config/environments.yml): Environment-specific settings
- [Terraform Documentation](infrastructure/README.md): Infrastructure details

## 🆘 Troubleshooting

### Common Issues

1. **ECS Tasks Not Starting**
   - Check CloudWatch logs
   - Verify task definition
   - Check security groups

2. **Database Connection Issues**
   - Verify RDS security groups
   - Check parameter store values
   - Test connectivity from ECS

3. **GitHub Actions Failures**
   - Check repository secrets
   - Verify AWS permissions
   - Review workflow logs

### Support Commands

```bash
# Check deployment status
./scripts/monitor.bat status prod

# View recent logs
./scripts/monitor.bat logs prod

# Run health checks
./scripts/validate-deployment.sh quick

# Check CloudWatch alarms
./scripts/monitor.bat alarms prod
```

## 🤝 Contributing

1. Create a feature branch
2. Make your changes
3. Run tests locally: `docker-compose up --build`
4. Create a pull request
5. Ensure all CI checks pass
6. Merge after approval

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙋‍♂️ Support

For support and questions:
- Check the troubleshooting section above
- Review CloudWatch logs and metrics
- Create an issue in the repository
- Contact the DevOps team

---

**Note**: This CI/CD pipeline is designed for production use with security best practices, monitoring, and automated testing. Regular maintenance and updates are recommended to keep the system secure and performant.
