@echo off
REM Expendra Master Deployment Script
REM This script orchestrates the complete deployment process
REM Usage: deploy.bat [environment] [action]

setlocal enabledelayedexpansion

REM Set default values
set ENV=%1
set ACTION=%2
if "%ENV%"=="" set ENV=prod
if "%ACTION%"=="" set ACTION=deploy

REM Configuration
set PROJECT_NAME=expendra
set AWS_REGION=us-east-1
set DOCKER_REGISTRY_PREFIX=%PROJECT_NAME%

REM Colors for output (if supported)
set GREEN=[32m
set RED=[31m
set YELLOW=[33m
set BLUE=[34m
set NC=[0m

echo.
echo %BLUE%========================================%NC%
echo %BLUE%    Expendra Deployment Script v1.0    %NC%
echo %BLUE%========================================%NC%
echo.
echo Environment: %GREEN%%ENV%%NC%
echo Action: %GREEN%%ACTION%%NC%
echo AWS Region: %GREEN%%AWS_REGION%%NC%
echo.

if "%ACTION%"=="help" goto :help
if "%ACTION%"=="init" goto :init
if "%ACTION%"=="build" goto :build
if "%ACTION%"=="deploy" goto :deploy
if "%ACTION%"=="rollback" goto :rollback
if "%ACTION%"=="status" goto :status
if "%ACTION%"=="logs" goto :logs
if "%ACTION%"=="destroy" goto :destroy
goto :help

:help
echo Available actions:
echo   init      - Initialize infrastructure and dependencies
echo   build     - Build and push Docker images
echo   deploy    - Deploy application to AWS
echo   rollback  - Rollback to previous deployment
echo   status    - Check deployment status
echo   logs      - View application logs
echo   destroy   - Destroy all infrastructure
echo   help      - Show this help message
echo.
echo Usage: deploy.bat [environment] [action]
echo   environment: dev, staging, prod (default: prod)
echo   action: init, build, deploy, rollback, status, logs, destroy, help
echo.
goto :end

:init
echo %YELLOW%Initializing infrastructure for %ENV% environment...%NC%
echo.

REM Check prerequisites
call :check_prerequisites
if !ERRORLEVEL! NEQ 0 goto :error

REM Initialize Terraform
echo %BLUE%Step 1: Initializing Terraform...%NC%
cd infrastructure
terraform init
if !ERRORLEVEL! NEQ 0 (
    echo %RED%Error: Terraform initialization failed%NC%
    goto :error
)

REM Create terraform.tfvars if it doesn't exist
if not exist terraform.tfvars (
    echo %YELLOW%Creating terraform.tfvars from example...%NC%
    copy terraform.tfvars.example terraform.tfvars
    echo %YELLOW%Please edit terraform.tfvars with your specific values%NC%
    pause
)

REM Plan infrastructure
echo %BLUE%Step 2: Planning infrastructure...%NC%
terraform plan -var="environment=%ENV%"
if !ERRORLEVEL! NEQ 0 (
    echo %RED%Error: Terraform planning failed%NC%
    goto :error
)

REM Apply infrastructure
echo %BLUE%Step 3: Creating infrastructure...%NC%
set /p confirm="Apply infrastructure changes? (y/N): "
if /i "!confirm!"=="y" (
    terraform apply -var="environment=%ENV%" -auto-approve
    if !ERRORLEVEL! NEQ 0 (
        echo %RED%Error: Infrastructure creation failed%NC%
        goto :error
    )
) else (
    echo %YELLOW%Infrastructure creation skipped%NC%
)

cd ..
echo %GREEN%Infrastructure initialization completed!%NC%
goto :end

:build
echo %YELLOW%Building and pushing Docker images...%NC%
echo.

REM Check prerequisites
call :check_prerequisites
if !ERRORLEVEL! NEQ 0 goto :error

REM Get ECR login
echo %BLUE%Step 1: Logging into ECR...%NC%
aws ecr get-login-password --region %AWS_REGION% | docker login --username AWS --password-stdin %AWS_ACCOUNT_ID%.dkr.ecr.%AWS_REGION%.amazonaws.com
if !ERRORLEVEL! NEQ 0 (
    echo %RED%Error: ECR login failed%NC%
    goto :error
)

REM Build backend image
echo %BLUE%Step 2: Building backend image...%NC%
cd backend
docker build -t %PROJECT_NAME%-backend:%ENV% .
if !ERRORLEVEL! NEQ 0 (
    echo %RED%Error: Backend build failed%NC%
    goto :error
)

REM Tag and push backend image
docker tag %PROJECT_NAME%-backend:%ENV% %AWS_ACCOUNT_ID%.dkr.ecr.%AWS_REGION%.amazonaws.com/%PROJECT_NAME%-backend:%ENV%
docker push %AWS_ACCOUNT_ID%.dkr.ecr.%AWS_REGION%.amazonaws.com/%PROJECT_NAME%-backend:%ENV%
if !ERRORLEVEL! NEQ 0 (
    echo %RED%Error: Backend push failed%NC%
    goto :error
)
cd ..

REM Build frontend image
echo %BLUE%Step 3: Building frontend image...%NC%
cd frontend
docker build -t %PROJECT_NAME%-frontend:%ENV% .
if !ERRORLEVEL! NEQ 0 (
    echo %RED%Error: Frontend build failed%NC%
    goto :error
)

REM Tag and push frontend image
docker tag %PROJECT_NAME%-frontend:%ENV% %AWS_ACCOUNT_ID%.dkr.ecr.%AWS_REGION%.amazonaws.com/%PROJECT_NAME%-frontend:%ENV%
docker push %AWS_ACCOUNT_ID%.dkr.ecr.%AWS_REGION%.amazonaws.com/%PROJECT_NAME%-frontend:%ENV%
if !ERRORLEVEL! NEQ 0 (
    echo %RED%Error: Frontend push failed%NC%
    goto :error
)
cd ..

echo %GREEN%Docker images built and pushed successfully!%NC%
goto :end

:deploy
echo %YELLOW%Deploying application to %ENV% environment...%NC%
echo.

REM Build images first
call :build
if !ERRORLEVEL! NEQ 0 goto :error

REM Update ECS services
echo %BLUE%Step 4: Updating ECS services...%NC%
aws ecs update-service --cluster %PROJECT_NAME%-%ENV%-cluster --service %PROJECT_NAME%-backend-%ENV% --force-new-deployment
aws ecs update-service --cluster %PROJECT_NAME%-%ENV%-cluster --service %PROJECT_NAME%-frontend-%ENV% --force-new-deployment

REM Wait for deployment to complete
echo %BLUE%Step 5: Waiting for deployment to complete...%NC%
aws ecs wait services-stable --cluster %PROJECT_NAME%-%ENV%-cluster --services %PROJECT_NAME%-backend-%ENV% %PROJECT_NAME%-frontend-%ENV%

echo %GREEN%Deployment completed successfully!%NC%
call :status
goto :end

:rollback
echo %YELLOW%Rolling back %ENV% environment...%NC%
echo.
echo %RED%Rollback functionality not yet implemented%NC%
echo %YELLOW%Please use AWS Console or CLI to rollback manually%NC%
goto :end

:status
echo %YELLOW%Checking deployment status for %ENV% environment...%NC%
echo.

REM Get service status
aws ecs describe-services --cluster %PROJECT_NAME%-%ENV%-cluster --services %PROJECT_NAME%-backend-%ENV% %PROJECT_NAME%-frontend-%ENV% --query "services[*].[serviceName,status,runningCount,desiredCount]" --output table

REM Get application URL
cd infrastructure
set APP_URL=
for /f "tokens=*" %%i in ('terraform output -raw application_url 2^>nul') do set APP_URL=%%i
if not "!APP_URL!"=="" (
    echo.
    echo %GREEN%Application URL: !APP_URL!%NC%
)
cd ..
goto :end

:logs
echo %YELLOW%Fetching logs for %ENV% environment...%NC%
echo.
aws logs tail /aws/ecs/%PROJECT_NAME%-%ENV% --follow
goto :end

:destroy
echo %RED%WARNING: This will destroy all infrastructure for %ENV% environment!%NC%
echo %RED%This action cannot be undone!%NC%
echo.
set /p confirm="Are you absolutely sure? Type 'yes' to confirm: "
if "!confirm!"=="yes" (
    cd infrastructure
    terraform destroy -var="environment=%ENV%" -auto-approve
    cd ..
    echo %GREEN%Infrastructure destroyed successfully%NC%
) else (
    echo %YELLOW%Destruction cancelled%NC%
)
goto :end

:check_prerequisites
echo %BLUE%Checking prerequisites...%NC%

REM Check if AWS CLI is installed
aws --version >nul 2>&1
if !ERRORLEVEL! NEQ 0 (
    echo %RED%Error: AWS CLI not found. Please install AWS CLI%NC%
    exit /b 1
)

REM Check if Docker is installed
docker --version >nul 2>&1
if !ERRORLEVEL! NEQ 0 (
    echo %RED%Error: Docker not found. Please install Docker%NC%
    exit /b 1
)

REM Check if Terraform is installed
terraform --version >nul 2>&1
if !ERRORLEVEL! NEQ 0 (
    echo %RED%Error: Terraform not found. Please install Terraform%NC%
    exit /b 1
)

REM Get AWS account ID
for /f "tokens=*" %%i in ('aws sts get-caller-identity --query Account --output text 2^>nul') do set AWS_ACCOUNT_ID=%%i
if "!AWS_ACCOUNT_ID!"=="" (
    echo %RED%Error: Unable to get AWS account ID. Please check AWS credentials%NC%
    exit /b 1
)

echo %GREEN%Prerequisites check passed%NC%
exit /b 0

:error
echo.
echo %RED%Deployment failed! Check the error messages above.%NC%
exit /b 1

:end
echo.
echo %BLUE%Script completed.%NC%
