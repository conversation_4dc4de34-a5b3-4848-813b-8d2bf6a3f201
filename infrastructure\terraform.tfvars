# Expendra Terraform Variables
# Customized for <PERSON>'s deployment

# Project Configuration
project_name = "expendra"
environment  = "prod"
aws_region   = "us-east-1"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"

# Database Configuration
db_instance_class    = "db.t3.micro"  # Starting small, can scale up later
db_allocated_storage = 20
db_name             = "expendra"
db_username         = "expendra_user"

# ECS Configuration - Starting with minimal resources
backend_cpu           = 512   # 0.5 vCPU
backend_memory        = 1024  # 1 GB
frontend_cpu          = 256   # 0.25 vCPU
frontend_memory       = 512   # 0.5 GB
backend_desired_count = 1     # Starting with 1 instance, can scale up
frontend_desired_count = 1    # Starting with 1 instance, can scale up

# Domain Configuration (commented out for initial deployment)
# domain_name     = "expendra.yourdomain.com"
# certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"

# API Keys - Set via AWS Systems Manager Parameter Store
# Note: These values will be stored as SecureString parameters in AWS
google_api_key = "AIzaSyBn3seHQKN2_E3OxRVNySedYoWmdqiSVas"
secret_key     = "expendra-super-secret-key-change-in-production-2024"

# Optional API keys (can be added later)
# openai_api_key    = "your-openai-key-here"
# anthropic_api_key = "your-anthropic-key-here"
