# Expendra Terraform Variables
# Customized for <PERSON>'s deployment

# Project Configuration
project_name = "expendra"
environment  = "prod"
aws_region   = "us-east-1"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"

# Database Configuration
db_instance_class    = "db.t3.micro"  # Starting small, can scale up later
db_allocated_storage = 20
db_name             = "expendra"
db_username         = "expendra_user"

# ECS Configuration - Starting with minimal resources
backend_cpu           = 512   # 0.5 vCPU
backend_memory        = 1024  # 1 GB
frontend_cpu          = 256   # 0.25 vCPU
frontend_memory       = 512   # 0.5 GB
backend_desired_count = 1     # Starting with 1 instance, can scale up
frontend_desired_count = 1    # Starting with 1 instance, can scale up

# Domain Configuration (commented out for initial deployment)
# domain_name     = "expendra.yourdomain.com"
# certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"

# API Keys will be set via AWS Systems Manager Parameter Store
# These are just placeholders - actual values will be set during deployment
# openai_api_key    = "placeholder"
# anthropic_api_key = "placeholder"
# google_api_key    = "placeholder"
# secret_key        = "placeholder"
