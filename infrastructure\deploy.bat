@echo off
REM Expendra Infrastructure Deployment Script
REM Usage: deploy.bat [command] [environment]

if "%1"=="" goto :help
if "%1"=="help" goto :help
if "%1"=="init" goto :init
if "%1"=="plan" goto :plan
if "%1"=="apply" goto :apply
if "%1"=="destroy" goto :destroy
if "%1"=="output" goto :output
goto :help

:help
echo.
echo Expendra Infrastructure Deployment Script
echo ========================================
echo.
echo Available commands:
echo   init       - Initialize Terraform
echo   plan       - Show Terraform execution plan
echo   apply      - Apply Terraform configuration
echo   destroy    - Destroy Terraform-managed infrastructure
echo   output     - Show Terraform outputs
echo   help       - Show this help message
echo.
echo Usage: deploy.bat [command] [environment]
echo Environment defaults to 'prod' if not specified
echo.
goto :end

:init
echo Initializing Terraform...
terraform init
if %ERRORLEVEL% NEQ 0 (
    echo Error: Terraform initialization failed
    goto :end
)
echo Terraform initialized successfully!
goto :end

:plan
set ENV=%2
if "%ENV%"=="" set ENV=prod
echo Planning Terraform deployment for environment: %ENV%
terraform plan -var="environment=%ENV%" -var-file="terraform.tfvars"
goto :end

:apply
set ENV=%2
if "%ENV%"=="" set ENV=prod
echo Applying Terraform configuration for environment: %ENV%
echo.
set /p confirm="Are you sure you want to apply these changes? (y/N): "
if /i "%confirm%"=="y" (
    terraform apply -var="environment=%ENV%" -var-file="terraform.tfvars"
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo Deployment completed successfully!
        echo.
        echo Application URLs:
        terraform output application_url
        terraform output backend_api_url
    ) else (
        echo Error: Terraform apply failed
    )
) else (
    echo Deployment cancelled.
)
goto :end

:destroy
set ENV=%2
if "%ENV%"=="" set ENV=prod
echo WARNING: This will destroy all infrastructure for environment: %ENV%
echo This action cannot be undone!
echo.
set /p confirm="Are you absolutely sure? Type 'yes' to confirm: "
if "%confirm%"=="yes" (
    terraform destroy -var="environment=%ENV%" -var-file="terraform.tfvars"
    if %ERRORLEVEL% EQU 0 (
        echo Infrastructure destroyed successfully.
    ) else (
        echo Error: Terraform destroy failed
    )
) else (
    echo Destruction cancelled.
)
goto :end

:output
echo Terraform Outputs:
echo ==================
terraform output
goto :end

:end
