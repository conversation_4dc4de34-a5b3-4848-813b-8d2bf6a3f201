import os
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

# Import database and models
from database import create_tables
from routes.auth import router as auth_router
from routes.settings import router as settings_router
from routes.research import router as research_router
from routes.dashboard import router as dashboard_router

# Import AWS configuration and logging
try:
    from config.aws_config import load_config_from_aws
    # Load configuration from AWS Systems Manager Parameter Store
    aws_config = load_config_from_aws()
    # Update environment variables with AWS config
    os.environ.update(aws_config)
    print("✅ Loaded configuration from AWS Systems Manager Parameter Store")
except Exception as e:
    print(f"⚠️  Failed to load AWS configuration: {e}")
    print("📁 Falling back to local .env file")

# Load environment variables from .env file (fallback)
load_dotenv()

# Setup logging (must be done after environment variables are loaded)
from config.logging_config import setup_logging, get_logger
setup_logging(
    log_level=os.getenv('LOG_LEVEL', 'INFO'),
    environment=os.getenv('ENVIRONMENT', 'prod'),
    service_name='expendra-backend'
)

# Get logger for this module
logger = get_logger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Expendra API",
    description="AI Purchasing Assistant Backend",
    version="0.1.0",
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    import time
    from config.logging_config import log_request_response

    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time

    # Log the request/response
    log_request_response(
        logger=logger,
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        response_time=process_time,
        request_id=getattr(request.state, 'request_id', None)
    )

    return response

# Add global OPTIONS handler for CORS preflight
@app.options("/{full_path:path}")
async def options_handler():
    """Handle CORS preflight requests for all routes."""
    return {"message": "OK"}

# Register API routes
app.include_router(auth_router, prefix="/api/v1")
app.include_router(settings_router, prefix="/api/v1")
app.include_router(research_router, prefix="/api/v1")
app.include_router(dashboard_router, prefix="/api/v1")

# Database initialization
@app.on_event("startup")
async def startup_event():
    """
    Initialize database tables on startup.
    """
    logger.info("Starting Expendra Backend API", extra={'event_type': 'startup'})
    try:
        create_tables()
        logger.info("Database tables created successfully", extra={'event_type': 'database_init'})
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}", extra={'event_type': 'database_init_error'})
        raise
    logger.info("Expendra backend started successfully!", extra={'event_type': 'startup_complete'})

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """
    Cleanup on shutdown.
    """
    logger.info("Shutting down Expendra Backend API", extra={'event_type': 'shutdown'})

# --- API Endpoints ---

@app.get("/", summary="Root endpoint")
async def read_root():
    """
    Root endpoint for the API. Returns service status.
    """
    return {"message": "Welcome to the Expendra API!", "version": "0.1.0"}

@app.get("/health", summary="Health check endpoint")
async def health_check():
    """
    Health check endpoint to verify API service status.
    """
    return {"status": "healthy", "message": "Expendra API is running"}

@app.get("/api/health", summary="API Health check endpoint")
async def api_health_check():
    """
    API Health check endpoint to verify API service status via /api path.
    """
    return {"status": "healthy", "message": "Expendra API is running"}

# --- Add other API routes here ---
# Example:
# from myapp.api.v1.endpoints.auth import router as auth_router
# app.include_router(auth_router, prefix="/api/v1")

# --- Background Tasks and Setup ---
# For example, to initialize agents or connect to external services upon startup.
# This would be a place to set up CrewAI agents or connect to MCP services.
# @app.on_event("startup")
# async def startup_event():
#     # Initialize AI agents or other services here
#     # This could involve fetching configurations or libraries using MCP tools if needed.
#     print("Starting Expendra backend...")
#     pass

# @app.on_event("shutdown")
# async def shutdown_event():
#     print("Shutting down Expendra backend...")
#     pass

if __name__ == "__main__":
    import uvicorn
    # This block is for running the app directly during development
    # In production, you'd typically use a more robust deployment method (e.g., Gunicorn with Uvicorn workers, or Docker)
    print("Run this application with: uvicorn backend.main:app --reload")
    # uvicorn.run(app, host="0.0.0.0", port=8000) # Uncomment to run directly