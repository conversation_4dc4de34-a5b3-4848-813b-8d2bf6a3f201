name: Security and Dependency Checks

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # Backend Security Scan
  backend-security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install safety bandit

    - name: Run Safety check (dependency vulnerabilities)
      run: |
        cd backend
        safety check --json --output safety-report.json || true

    - name: Run Bandit security linter
      run: |
        cd backend
        bandit -r . -f json -o bandit-report.json || true

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: backend-security-reports
        path: |
          backend/safety-report.json
          backend/bandit-report.json

  # Frontend Security Scan
  frontend-security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      run: |
        cd frontend
        npm ci

    - name: Run npm audit
      run: |
        cd frontend
        npm audit --audit-level=moderate --json > npm-audit-report.json || true

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: frontend-security-reports
        path: frontend/npm-audit-report.json

  # Docker Image Security Scan
  docker-security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Build Docker images
      run: |
        docker build -t expendra-backend:test ./backend
        docker build -t expendra-frontend:test ./frontend

    - name: Run Trivy vulnerability scanner - Backend
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'expendra-backend:test'
        format: 'sarif'
        output: 'backend-trivy-results.sarif'

    - name: Run Trivy vulnerability scanner - Frontend
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'expendra-frontend:test'
        format: 'sarif'
        output: 'frontend-trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'backend-trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'frontend-trivy-results.sarif'

  # CodeQL Analysis
  codeql-analysis:
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'python', 'javascript' ]

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: ${{ matrix.language }}

    - name: Autobuild
      uses: github/codeql-action/autobuild@v2

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
