# Core FastAPI Dependencies (Fast Build)
fastapi
uvicorn[standard]
SQLAlchemy
psycopg2-binary
python-dotenv
pydantic[email]
websockets
httpx
python-jose[cryptography]
passlib[bcrypt]
aiofiles

# Basic Web Scraping (lightweight)
beautifulsoup4
requests

# Essential AI Dependencies (only what's actually imported)
google-generativeai
crewai>=0.134.0
langchain-core
langchain-google-genai>=1.0.0

# TODO: Add back heavy dependencies after initial deployment
# playwright
# pandas
# reportlab
# lxml
# langchain>=0.1.0
# langchain-openai>=0.1.0
# langchain-community>=0.0.20
# openai>=1.0.0