version: 2
updates:
  # Backend Python dependencies
  - package-ecosystem: "pip"
    directory: "/backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "PapaBear1981"
    assignees:
      - "PapaBear1981"
    commit-message:
      prefix: "backend"
      include: "scope"
    labels:
      - "dependencies"
      - "backend"
      - "python"

  # Frontend npm dependencies
  - package-ecosystem: "npm"
    directory: "/frontend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "PapaBear1981"
    assignees:
      - "PapaBear1981"
    commit-message:
      prefix: "frontend"
      include: "scope"
    labels:
      - "dependencies"
      - "frontend"
      - "javascript"

  # Root npm dependencies
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "PapaBear1981"
    assignees:
      - "PapaBear1981"
    commit-message:
      prefix: "root"
      include: "scope"
    labels:
      - "dependencies"
      - "root"

  # Docker dependencies
  - package-ecosystem: "docker"
    directory: "/backend"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
    reviewers:
      - "PapaBear1981"
    assignees:
      - "PapaBear1981"
    commit-message:
      prefix: "docker"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"
      - "backend"

  - package-ecosystem: "docker"
    directory: "/frontend"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
    reviewers:
      - "PapaBear1981"
    assignees:
      - "PapaBear1981"
    commit-message:
      prefix: "docker"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"
      - "frontend"

  # GitHub Actions dependencies
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "wednesday"
      time: "09:00"
    reviewers:
      - "PapaBear1981"
    assignees:
      - "PapaBear1981"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
      - "ci-cd"
