version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: expendra-db
    environment:
      POSTGRES_DB: expendra
      POSTGRES_USER: expendra_user
      POSTGRES_PASSWORD: expendra_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U expendra_user -d expendra"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - expendra-network

  # Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: expendra-backend
    environment:
      - DATABASE_URL=**********************************************************/expendra
      - SECRET_KEY=dev-secret-key-change-in-production
      - DEBUG=true
      - LOG_LEVEL=info
    volumes:
      - ./backend:/app
      - /app/venv  # Exclude venv from volume mount
    ports:
      - "8000:8000"
    depends_on:
      database:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - expendra-network
    restart: unless-stopped

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: expendra-frontend
    environment:
      - VITE_API_BASE_URL=http://localhost:8000/api/v1
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - expendra-network
    restart: unless-stopped

  # Redis for caching (optional, for future use)
  redis:
    image: redis:7-alpine
    container_name: expendra-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - expendra-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  expendra-network:
    driver: bridge
