"""
AWS Configuration and Secrets Management for Expendra
This module handles loading configuration from AWS Systems Manager Parameter Store
"""

import os
import boto3
import logging
from typing import Dict, Optional
from botocore.exceptions import ClientError, NoCredentialsError

logger = logging.getLogger(__name__)


class AWSConfig:
    """Manages configuration from AWS Systems Manager Parameter Store"""
    
    def __init__(self, environment: str = "prod", region: str = "us-east-1"):
        """
        Initialize AWS configuration manager
        
        Args:
            environment: Environment name (dev, staging, prod)
            region: AWS region
        """
        self.environment = environment
        self.region = region
        self.parameter_prefix = f"/expendra-{environment}"
        
        try:
            self.ssm_client = boto3.client('ssm', region_name=region)
            self._test_connection()
        except NoCredentialsError:
            logger.warning("AWS credentials not found. Using local environment variables.")
            self.ssm_client = None
        except Exception as e:
            logger.error(f"Failed to initialize AWS SSM client: {e}")
            self.ssm_client = None
    
    def _test_connection(self):
        """Test AWS SSM connection"""
        try:
            self.ssm_client.describe_parameters(MaxResults=1)
            logger.info("AWS SSM connection successful")
        except ClientError as e:
            logger.warning(f"AWS SSM connection test failed: {e}")
            raise
    
    def get_parameter(self, parameter_name: str, decrypt: bool = True) -> Optional[str]:
        """
        Get a parameter from AWS Systems Manager Parameter Store
        
        Args:
            parameter_name: Name of the parameter (without prefix)
            decrypt: Whether to decrypt SecureString parameters
            
        Returns:
            Parameter value or None if not found
        """
        if not self.ssm_client:
            # Fallback to environment variables
            env_var_name = parameter_name.replace('/', '_').replace('-', '_').upper()
            return os.getenv(env_var_name)
        
        full_parameter_name = f"{self.parameter_prefix}/{parameter_name}"
        
        try:
            response = self.ssm_client.get_parameter(
                Name=full_parameter_name,
                WithDecryption=decrypt
            )
            return response['Parameter']['Value']
        except ClientError as e:
            if e.response['Error']['Code'] == 'ParameterNotFound':
                logger.warning(f"Parameter not found: {full_parameter_name}")
                # Fallback to environment variables
                env_var_name = parameter_name.replace('/', '_').replace('-', '_').upper()
                return os.getenv(env_var_name)
            else:
                logger.error(f"Error getting parameter {full_parameter_name}: {e}")
                return None
    
    def get_parameters_by_path(self, path: str, decrypt: bool = True) -> Dict[str, str]:
        """
        Get multiple parameters by path
        
        Args:
            path: Parameter path (without prefix)
            decrypt: Whether to decrypt SecureString parameters
            
        Returns:
            Dictionary of parameter names and values
        """
        if not self.ssm_client:
            logger.warning("AWS SSM client not available. Cannot get parameters by path.")
            return {}
        
        full_path = f"{self.parameter_prefix}/{path}"
        parameters = {}
        
        try:
            paginator = self.ssm_client.get_paginator('get_parameters_by_path')
            page_iterator = paginator.paginate(
                Path=full_path,
                Recursive=True,
                WithDecryption=decrypt
            )
            
            for page in page_iterator:
                for param in page['Parameters']:
                    # Remove the prefix to get the relative parameter name
                    param_name = param['Name'].replace(f"{self.parameter_prefix}/", "")
                    parameters[param_name] = param['Value']
                    
        except ClientError as e:
            logger.error(f"Error getting parameters by path {full_path}: {e}")
        
        return parameters
    
    def get_database_config(self) -> Dict[str, str]:
        """Get database configuration"""
        return {
            'url': self.get_parameter('database/url'),
            'password': self.get_parameter('database/password')
        }
    
    def get_api_keys(self) -> Dict[str, str]:
        """Get all API keys"""
        api_keys = {}
        
        # Get individual API keys
        openai_key = self.get_parameter('api-keys/openai')
        if openai_key:
            api_keys['openai'] = openai_key
            
        anthropic_key = self.get_parameter('api-keys/anthropic')
        if anthropic_key:
            api_keys['anthropic'] = anthropic_key
            
        google_key = self.get_parameter('api-keys/google')
        if google_key:
            api_keys['google'] = google_key
        
        return api_keys
    
    def get_app_config(self) -> Dict[str, str]:
        """Get application configuration"""
        config = {}
        
        # Get application secret key
        secret_key = self.get_parameter('app/secret-key')
        if secret_key:
            config['secret_key'] = secret_key
        
        # Get other app configuration
        app_config = self.get_parameters_by_path('app/config', decrypt=False)
        config.update(app_config)
        
        return config


# Global configuration instance
_aws_config = None


def get_aws_config(environment: str = None, region: str = None) -> AWSConfig:
    """
    Get or create AWS configuration instance
    
    Args:
        environment: Environment name (defaults to ENVIRONMENT env var or 'prod')
        region: AWS region (defaults to AWS_REGION env var or 'us-east-1')
        
    Returns:
        AWSConfig instance
    """
    global _aws_config
    
    if _aws_config is None:
        env = environment or os.getenv('ENVIRONMENT', 'prod')
        reg = region or os.getenv('AWS_REGION', 'us-east-1')
        _aws_config = AWSConfig(environment=env, region=reg)
    
    return _aws_config


def load_config_from_aws() -> Dict[str, str]:
    """
    Load all configuration from AWS and return as environment variables
    
    Returns:
        Dictionary of configuration values
    """
    aws_config = get_aws_config()
    config = {}
    
    # Database configuration
    db_config = aws_config.get_database_config()
    if db_config.get('url'):
        config['DATABASE_URL'] = db_config['url']
    
    # API keys
    api_keys = aws_config.get_api_keys()
    for key, value in api_keys.items():
        config[f'{key.upper()}_API_KEY'] = value
    
    # App configuration
    app_config = aws_config.get_app_config()
    if app_config.get('secret_key'):
        config['SECRET_KEY'] = app_config['secret_key']
    
    # Other app settings
    for key, value in app_config.items():
        if key != 'secret_key':
            config[key.upper().replace('-', '_')] = value
    
    return config
