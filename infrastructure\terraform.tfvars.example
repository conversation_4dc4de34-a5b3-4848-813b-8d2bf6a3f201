# Expendra Terraform Variables Example
# Copy this file to terraform.tfvars and customize for your environment

# Project Configuration
project_name = "expendra"
environment  = "prod"
aws_region   = "us-east-1"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"

# Database Configuration
db_instance_class    = "db.t3.micro"  # Use db.t3.small or larger for production
db_allocated_storage = 20
db_name             = "expendra"
db_username         = "expendra_user"

# ECS Configuration
backend_cpu           = 512   # 0.5 vCPU
backend_memory        = 1024  # 1 GB
frontend_cpu          = 256   # 0.25 vCPU
frontend_memory       = 512   # 0.5 GB
backend_desired_count = 2
frontend_desired_count = 2

# Domain Configuration (optional)
# domain_name     = "expendra.yourdomain.com"
# certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"

# API Keys (set these as environment variables or use AWS Secrets Manager)
# openai_api_key    = "your-openai-api-key"
# anthropic_api_key = "your-anthropic-api-key"
# google_api_key    = "your-google-api-key"
# secret_key        = "your-application-secret-key"
