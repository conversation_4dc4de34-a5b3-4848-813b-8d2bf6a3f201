@echo off
REM Expendra Monitoring and Health Check Script
REM Usage: monitor.bat [command] [environment]

setlocal enabledelayedexpansion

set ENV=%2
set COMMAND=%1
if "%ENV%"=="" set ENV=prod
if "%COMMAND%"=="" set COMMAND=status

REM Configuration
set PROJECT_NAME=expendra
set AWS_REGION=us-east-1

REM Colors for output
set GREEN=[32m
set RED=[31m
set YELLOW=[33m
set BLUE=[34m
set NC=[0m

echo.
echo %BLUE%========================================%NC%
echo %BLUE%    Expendra Monitoring Script v1.0    %NC%
echo %BLUE%========================================%NC%
echo.
echo Environment: %GREEN%%ENV%%NC%
echo Command: %GREEN%%COMMAND%%NC%
echo.

if "%COMMAND%"=="help" goto :help
if "%COMMAND%"=="status" goto :status
if "%COMMAND%"=="health" goto :health
if "%COMMAND%"=="logs" goto :logs
if "%COMMAND%"=="metrics" goto :metrics
if "%COMMAND%"=="alarms" goto :alarms
if "%COMMAND%"=="dashboard" goto :dashboard
goto :help

:help
echo Available commands:
echo   status     - Check overall system status
echo   health     - Run health checks
echo   logs       - View recent logs
echo   metrics    - Show key metrics
echo   alarms     - Check CloudWatch alarms
echo   dashboard  - Open CloudWatch dashboard
echo   help       - Show this help message
echo.
echo Usage: monitor.bat [command] [environment]
echo   environment: dev, staging, prod (default: prod)
echo   command: status, health, logs, metrics, alarms, dashboard, help
echo.
goto :end

:status
echo %YELLOW%Checking system status for %ENV% environment...%NC%
echo.

REM Check ECS services
echo %BLUE%ECS Services:%NC%
aws ecs describe-services --cluster %PROJECT_NAME%-%ENV%-cluster --services %PROJECT_NAME%-backend-%ENV% %PROJECT_NAME%-frontend-%ENV% --query "services[*].[serviceName,status,runningCount,desiredCount,pendingCount]" --output table

echo.
echo %BLUE%ECS Tasks:%NC%
aws ecs list-tasks --cluster %PROJECT_NAME%-%ENV%-cluster --query "taskArns[*]" --output table

echo.
echo %BLUE%Load Balancer Health:%NC%
aws elbv2 describe-target-health --target-group-arn $(aws elbv2 describe-target-groups --names %PROJECT_NAME%-backend-%ENV%-tg --query "TargetGroups[0].TargetGroupArn" --output text) --query "TargetHealthDescriptions[*].[Target.Id,TargetHealth.State,TargetHealth.Description]" --output table

echo.
echo %BLUE%RDS Status:%NC%
aws rds describe-db-instances --db-instance-identifier %PROJECT_NAME%-%ENV%-database --query "DBInstances[0].[DBInstanceIdentifier,DBInstanceStatus,Engine,DBInstanceClass,AllocatedStorage]" --output table

goto :end

:health
echo %YELLOW%Running health checks for %ENV% environment...%NC%
echo.

REM Get application URL
cd infrastructure
set APP_URL=
for /f "tokens=*" %%i in ('terraform output -raw application_url 2^>nul') do set APP_URL=%%i
cd ..

if "!APP_URL!"=="" (
    echo %RED%Error: Could not get application URL from Terraform%NC%
    goto :end
)

echo %BLUE%Testing application endpoints:%NC%
echo Application URL: !APP_URL!
echo.

REM Test backend health endpoint
echo %YELLOW%Testing backend health...%NC%
curl -s -o nul -w "Backend Health: %%{http_code} - %%{time_total}s\n" !APP_URL!/api/health
if !ERRORLEVEL! EQU 0 (
    echo %GREEN%✓ Backend health check passed%NC%
) else (
    echo %RED%✗ Backend health check failed%NC%
)

REM Test frontend
echo %YELLOW%Testing frontend...%NC%
curl -s -o nul -w "Frontend: %%{http_code} - %%{time_total}s\n" !APP_URL!/
if !ERRORLEVEL! EQU 0 (
    echo %GREEN%✓ Frontend check passed%NC%
) else (
    echo %RED%✗ Frontend check failed%NC%
)

REM Test API endpoints
echo %YELLOW%Testing API endpoints...%NC%
curl -s -o nul -w "API Root: %%{http_code} - %%{time_total}s\n" !APP_URL!/api/v1/
if !ERRORLEVEL! EQU 0 (
    echo %GREEN%✓ API root check passed%NC%
) else (
    echo %RED%✗ API root check failed%NC%
)

goto :end

:logs
echo %YELLOW%Fetching recent logs for %ENV% environment...%NC%
echo.

set /p service="Enter service (backend/frontend/all): "
if "%service%"=="" set service=all

if "%service%"=="backend" goto :logs_backend
if "%service%"=="frontend" goto :logs_frontend
if "%service%"=="all" goto :logs_all
goto :logs_all

:logs_backend
echo %BLUE%Backend Logs:%NC%
aws logs tail /aws/ecs/%PROJECT_NAME%-%ENV%-backend --since 1h --format short
goto :end

:logs_frontend
echo %BLUE%Frontend Logs:%NC%
aws logs tail /aws/ecs/%PROJECT_NAME%-%ENV%-frontend --since 1h --format short
goto :end

:logs_all
echo %BLUE%Backend Logs (last 20 entries):%NC%
aws logs tail /aws/ecs/%PROJECT_NAME%-%ENV%-backend --since 1h --format short | head -20
echo.
echo %BLUE%Frontend Logs (last 20 entries):%NC%
aws logs tail /aws/ecs/%PROJECT_NAME%-%ENV%-frontend --since 1h --format short | head -20
goto :end

:metrics
echo %YELLOW%Showing key metrics for %ENV% environment...%NC%
echo.

REM Get current time for metrics query
for /f "tokens=*" %%i in ('powershell -command "(Get-Date).AddHours(-1).ToString('yyyy-MM-ddTHH:mm:ssZ')"') do set START_TIME=%%i
for /f "tokens=*" %%i in ('powershell -command "(Get-Date).ToString('yyyy-MM-ddTHH:mm:ssZ')"') do set END_TIME=%%i

echo %BLUE%ECS Service Metrics (last hour):%NC%
aws cloudwatch get-metric-statistics --namespace AWS/ECS --metric-name CPUUtilization --dimensions Name=ServiceName,Value=%PROJECT_NAME%-backend-%ENV% Name=ClusterName,Value=%PROJECT_NAME%-%ENV%-cluster --start-time %START_TIME% --end-time %END_TIME% --period 3600 --statistics Average --query "Datapoints[0].Average" --output text

echo %BLUE%ALB Request Count (last hour):%NC%
aws cloudwatch get-metric-statistics --namespace AWS/ApplicationELB --metric-name RequestCount --dimensions Name=LoadBalancer,Value=app/%PROJECT_NAME%-%ENV%-alb/* --start-time %START_TIME% --end-time %END_TIME% --period 3600 --statistics Sum --query "Datapoints[0].Sum" --output text

echo %BLUE%RDS CPU Utilization (last hour):%NC%
aws cloudwatch get-metric-statistics --namespace AWS/RDS --metric-name CPUUtilization --dimensions Name=DBInstanceIdentifier,Value=%PROJECT_NAME%-%ENV%-database --start-time %START_TIME% --end-time %END_TIME% --period 3600 --statistics Average --query "Datapoints[0].Average" --output text

goto :end

:alarms
echo %YELLOW%Checking CloudWatch alarms for %ENV% environment...%NC%
echo.

aws cloudwatch describe-alarms --alarm-name-prefix %PROJECT_NAME%-%ENV% --query "MetricAlarms[*].[AlarmName,StateValue,StateReason]" --output table

goto :end

:dashboard
echo %YELLOW%Opening CloudWatch dashboard for %ENV% environment...%NC%
echo.

set DASHBOARD_URL=https://%AWS_REGION%.console.aws.amazon.com/cloudwatch/home?region=%AWS_REGION%#dashboards:name=%PROJECT_NAME%-%ENV%-dashboard

echo Opening dashboard: %DASHBOARD_URL%
start "" "%DASHBOARD_URL%"

goto :end

:end
echo.
echo %BLUE%Monitoring script completed.%NC%
