@echo off
REM Expendra Docker Management Scripts
REM Usage: docker-scripts.bat [command]

if "%1"=="" goto :help
if "%1"=="help" goto :help
if "%1"=="build" goto :build
if "%1"=="up" goto :up
if "%1"=="down" goto :down
if "%1"=="logs" goto :logs
if "%1"=="clean" goto :clean
if "%1"=="prod-up" goto :prod-up
if "%1"=="prod-down" goto :prod-down
goto :help

:help
echo.
echo Expendra Docker Management Scripts
echo ==================================
echo.
echo Available commands:
echo   build      - Build all Docker images
echo   up         - Start development environment
echo   down       - Stop development environment
echo   logs       - Show logs from all services
echo   clean      - Clean up Docker resources (images, containers, volumes)
echo   prod-up    - Start production environment
echo   prod-down  - Stop production environment
echo   help       - Show this help message
echo.
goto :end

:build
echo Building Docker images...
docker-compose build --no-cache
echo Build complete!
goto :end

:up
echo Starting Expendra development environment...
docker-compose up -d
echo.
echo Services starting up...
echo Frontend: http://localhost:3000
echo Backend API: http://localhost:8000
echo Database: localhost:5432
echo.
echo Use 'docker-scripts.bat logs' to view logs
echo Use 'docker-scripts.bat down' to stop services
goto :end

:down
echo Stopping Expendra development environment...
docker-compose down
echo Services stopped.
goto :end

:logs
echo Showing logs from all services...
docker-compose logs -f
goto :end

:clean
echo Cleaning up Docker resources...
echo This will remove all stopped containers, unused networks, and dangling images.
set /p confirm="Are you sure? (y/N): "
if /i "%confirm%"=="y" (
    docker system prune -f
    docker volume prune -f
    echo Cleanup complete!
) else (
    echo Cleanup cancelled.
)
goto :end

:prod-up
echo Starting Expendra production environment...
docker-compose -f docker-compose.prod.yml up -d
echo.
echo Production services starting up...
echo Frontend: http://localhost
echo Backend API: http://localhost:8000
echo.
goto :end

:prod-down
echo Stopping Expendra production environment...
docker-compose -f docker-compose.prod.yml down
echo Production services stopped.
goto :end

:end
