#!/bin/bash
# Expendra Deployment Validation Script
# This script validates that the CI/CD pipeline and deployment are working correctly

set -e

# Configuration
PROJECT_NAME="expendra"
AWS_REGION="${AWS_REGION:-us-east-1}"
ENVIRONMENT="${ENVIRONMENT:-prod}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((TESTS_PASSED++))
    ((TESTS_TOTAL++))
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((TESTS_FAILED++))
    ((TESTS_TOTAL++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Test functions
test_aws_credentials() {
    log_info "Testing AWS credentials..."
    if aws sts get-caller-identity &> /dev/null; then
        log_success "AWS credentials are valid"
        AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
        log_info "AWS Account ID: $AWS_ACCOUNT_ID"
    else
        log_error "AWS credentials are not configured or invalid"
        return 1
    fi
}

test_terraform_state() {
    log_info "Testing Terraform state..."
    cd infrastructure
    if terraform show &> /dev/null; then
        log_success "Terraform state is valid"
        
        # Check if infrastructure exists
        if terraform output application_url &> /dev/null; then
            APP_URL=$(terraform output -raw application_url)
            log_success "Infrastructure is deployed. App URL: $APP_URL"
        else
            log_warning "Infrastructure may not be fully deployed"
        fi
    else
        log_error "Terraform state is invalid or infrastructure not deployed"
        cd ..
        return 1
    fi
    cd ..
}

test_ecr_repositories() {
    log_info "Testing ECR repositories..."
    
    # Check backend repository
    if aws ecr describe-repositories --repository-names $PROJECT_NAME-backend --region $AWS_REGION &> /dev/null; then
        log_success "Backend ECR repository exists"
        
        # Check for images
        IMAGE_COUNT=$(aws ecr list-images --repository-name $PROJECT_NAME-backend --region $AWS_REGION --query 'length(imageIds)')
        if [ "$IMAGE_COUNT" -gt 0 ]; then
            log_success "Backend ECR repository has $IMAGE_COUNT images"
        else
            log_warning "Backend ECR repository has no images"
        fi
    else
        log_error "Backend ECR repository does not exist"
    fi
    
    # Check frontend repository
    if aws ecr describe-repositories --repository-names $PROJECT_NAME-frontend --region $AWS_REGION &> /dev/null; then
        log_success "Frontend ECR repository exists"
        
        # Check for images
        IMAGE_COUNT=$(aws ecr list-images --repository-name $PROJECT_NAME-frontend --region $AWS_REGION --query 'length(imageIds)')
        if [ "$IMAGE_COUNT" -gt 0 ]; then
            log_success "Frontend ECR repository has $IMAGE_COUNT images"
        else
            log_warning "Frontend ECR repository has no images"
        fi
    else
        log_error "Frontend ECR repository does not exist"
    fi
}

test_ecs_cluster() {
    log_info "Testing ECS cluster..."
    
    CLUSTER_NAME="$PROJECT_NAME-cluster"
    if [ "$ENVIRONMENT" != "prod" ]; then
        CLUSTER_NAME="$PROJECT_NAME-cluster-$ENVIRONMENT"
    fi
    
    if aws ecs describe-clusters --clusters $CLUSTER_NAME --region $AWS_REGION &> /dev/null; then
        CLUSTER_STATUS=$(aws ecs describe-clusters --clusters $CLUSTER_NAME --region $AWS_REGION --query 'clusters[0].status' --output text)
        if [ "$CLUSTER_STATUS" = "ACTIVE" ]; then
            log_success "ECS cluster $CLUSTER_NAME is active"
        else
            log_error "ECS cluster $CLUSTER_NAME status: $CLUSTER_STATUS"
        fi
    else
        log_error "ECS cluster $CLUSTER_NAME does not exist"
    fi
}

test_ecs_services() {
    log_info "Testing ECS services..."
    
    CLUSTER_NAME="$PROJECT_NAME-cluster"
    BACKEND_SERVICE="$PROJECT_NAME-backend-service"
    FRONTEND_SERVICE="$PROJECT_NAME-frontend-service"
    
    if [ "$ENVIRONMENT" != "prod" ]; then
        CLUSTER_NAME="$PROJECT_NAME-cluster-$ENVIRONMENT"
        BACKEND_SERVICE="$PROJECT_NAME-backend-service-$ENVIRONMENT"
        FRONTEND_SERVICE="$PROJECT_NAME-frontend-service-$ENVIRONMENT"
    fi
    
    # Test backend service
    if aws ecs describe-services --cluster $CLUSTER_NAME --services $BACKEND_SERVICE --region $AWS_REGION &> /dev/null; then
        BACKEND_STATUS=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $BACKEND_SERVICE --region $AWS_REGION --query 'services[0].status' --output text)
        BACKEND_RUNNING=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $BACKEND_SERVICE --region $AWS_REGION --query 'services[0].runningCount' --output text)
        BACKEND_DESIRED=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $BACKEND_SERVICE --region $AWS_REGION --query 'services[0].desiredCount' --output text)
        
        if [ "$BACKEND_STATUS" = "ACTIVE" ] && [ "$BACKEND_RUNNING" = "$BACKEND_DESIRED" ]; then
            log_success "Backend service is healthy ($BACKEND_RUNNING/$BACKEND_DESIRED tasks running)"
        else
            log_error "Backend service issues: Status=$BACKEND_STATUS, Running=$BACKEND_RUNNING, Desired=$BACKEND_DESIRED"
        fi
    else
        log_error "Backend service $BACKEND_SERVICE does not exist"
    fi
    
    # Test frontend service
    if aws ecs describe-services --cluster $CLUSTER_NAME --services $FRONTEND_SERVICE --region $AWS_REGION &> /dev/null; then
        FRONTEND_STATUS=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $FRONTEND_SERVICE --region $AWS_REGION --query 'services[0].status' --output text)
        FRONTEND_RUNNING=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $FRONTEND_SERVICE --region $AWS_REGION --query 'services[0].runningCount' --output text)
        FRONTEND_DESIRED=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $FRONTEND_SERVICE --region $AWS_REGION --query 'services[0].desiredCount' --output text)
        
        if [ "$FRONTEND_STATUS" = "ACTIVE" ] && [ "$FRONTEND_RUNNING" = "$FRONTEND_DESIRED" ]; then
            log_success "Frontend service is healthy ($FRONTEND_RUNNING/$FRONTEND_DESIRED tasks running)"
        else
            log_error "Frontend service issues: Status=$FRONTEND_STATUS, Running=$FRONTEND_RUNNING, Desired=$FRONTEND_DESIRED"
        fi
    else
        log_error "Frontend service $FRONTEND_SERVICE does not exist"
    fi
}

test_rds_database() {
    log_info "Testing RDS database..."
    
    DB_IDENTIFIER="$PROJECT_NAME-database"
    if [ "$ENVIRONMENT" != "prod" ]; then
        DB_IDENTIFIER="$PROJECT_NAME-database-$ENVIRONMENT"
    fi
    
    if aws rds describe-db-instances --db-instance-identifier $DB_IDENTIFIER --region $AWS_REGION &> /dev/null; then
        DB_STATUS=$(aws rds describe-db-instances --db-instance-identifier $DB_IDENTIFIER --region $AWS_REGION --query 'DBInstances[0].DBInstanceStatus' --output text)
        if [ "$DB_STATUS" = "available" ]; then
            log_success "RDS database $DB_IDENTIFIER is available"
        else
            log_error "RDS database $DB_IDENTIFIER status: $DB_STATUS"
        fi
    else
        log_error "RDS database $DB_IDENTIFIER does not exist"
    fi
}

test_application_endpoints() {
    log_info "Testing application endpoints..."
    
    if [ -z "$APP_URL" ]; then
        log_error "Application URL not available. Skipping endpoint tests."
        return 1
    fi
    
    # Test backend health endpoint
    log_info "Testing backend health endpoint..."
    if curl -f -s "$APP_URL/api/health" > /dev/null; then
        log_success "Backend health endpoint is responding"
    else
        log_error "Backend health endpoint is not responding"
    fi
    
    # Test frontend
    log_info "Testing frontend..."
    if curl -f -s "$APP_URL" > /dev/null; then
        log_success "Frontend is responding"
    else
        log_error "Frontend is not responding"
    fi
    
    # Test API root
    log_info "Testing API root endpoint..."
    if curl -f -s "$APP_URL/api/v1/" > /dev/null; then
        log_success "API root endpoint is responding"
    else
        log_error "API root endpoint is not responding"
    fi
}

test_cloudwatch_logs() {
    log_info "Testing CloudWatch logs..."
    
    # Check backend logs
    BACKEND_LOG_GROUP="/aws/ecs/$PROJECT_NAME-backend"
    if [ "$ENVIRONMENT" != "prod" ]; then
        BACKEND_LOG_GROUP="/aws/ecs/$PROJECT_NAME-backend-$ENVIRONMENT"
    fi
    
    if aws logs describe-log-groups --log-group-name-prefix "$BACKEND_LOG_GROUP" --region $AWS_REGION | grep -q "$BACKEND_LOG_GROUP"; then
        log_success "Backend CloudWatch log group exists"
    else
        log_error "Backend CloudWatch log group does not exist"
    fi
    
    # Check frontend logs
    FRONTEND_LOG_GROUP="/aws/ecs/$PROJECT_NAME-frontend"
    if [ "$ENVIRONMENT" != "prod" ]; then
        FRONTEND_LOG_GROUP="/aws/ecs/$PROJECT_NAME-frontend-$ENVIRONMENT"
    fi
    
    if aws logs describe-log-groups --log-group-name-prefix "$FRONTEND_LOG_GROUP" --region $AWS_REGION | grep -q "$FRONTEND_LOG_GROUP"; then
        log_success "Frontend CloudWatch log group exists"
    else
        log_error "Frontend CloudWatch log group does not exist"
    fi
}

test_ssm_parameters() {
    log_info "Testing SSM parameters..."
    
    PARAM_PREFIX="/$PROJECT_NAME-$ENVIRONMENT"
    
    # Check if any parameters exist with our prefix
    PARAM_COUNT=$(aws ssm get-parameters-by-path --path "$PARAM_PREFIX" --region $AWS_REGION --query 'length(Parameters)' 2>/dev/null || echo "0")
    
    if [ "$PARAM_COUNT" -gt 0 ]; then
        log_success "Found $PARAM_COUNT SSM parameters with prefix $PARAM_PREFIX"
    else
        log_warning "No SSM parameters found with prefix $PARAM_PREFIX"
    fi
}

# Main validation function
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    Expendra Deployment Validation     ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    echo "Environment: $ENVIRONMENT"
    echo "AWS Region: $AWS_REGION"
    echo ""
    
    # Run all tests
    test_aws_credentials
    test_terraform_state
    test_ecr_repositories
    test_ecs_cluster
    test_ecs_services
    test_rds_database
    test_application_endpoints
    test_cloudwatch_logs
    test_ssm_parameters
    
    # Summary
    echo ""
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}           Validation Summary           ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    echo "Total tests: $TESTS_TOTAL"
    echo -e "Passed: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "Failed: ${RED}$TESTS_FAILED${NC}"
    echo ""
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "${GREEN}✅ All tests passed! Deployment is healthy.${NC}"
        exit 0
    else
        echo -e "${RED}❌ Some tests failed. Please check the issues above.${NC}"
        exit 1
    fi
}

# Handle script arguments
case "${1:-validate}" in
    "validate")
        main
        ;;
    "quick")
        test_aws_credentials
        test_application_endpoints
        ;;
    "infrastructure")
        test_aws_credentials
        test_terraform_state
        test_ecr_repositories
        test_ecs_cluster
        test_ecs_services
        test_rds_database
        ;;
    *)
        echo "Usage: $0 {validate|quick|infrastructure}"
        echo "  validate       - Run all validation tests (default)"
        echo "  quick         - Run quick health checks only"
        echo "  infrastructure - Run infrastructure tests only"
        exit 1
        ;;
esac
