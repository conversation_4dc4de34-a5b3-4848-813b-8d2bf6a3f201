# SSM Parameter Store Module for Expendra

# Database URL (SecureString)
resource "aws_ssm_parameter" "database_url" {
  name  = "/${var.name_prefix}/database/url"
  type  = "SecureString"
  value = var.database_url

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-database-url"
  })
}

# Database Password (SecureString)
resource "aws_ssm_parameter" "database_password" {
  name  = "/${var.name_prefix}/database/password"
  type  = "SecureString"
  value = var.database_password

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-database-password"
  })
}

# Application Secret Key (SecureString)
resource "aws_ssm_parameter" "secret_key" {
  name  = "/${var.name_prefix}/app/secret-key"
  type  = "SecureString"
  value = var.secret_key != "" ? var.secret_key : random_password.secret_key.result

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-secret-key"
  })
}

# Generate random secret key if not provided
resource "random_password" "secret_key" {
  length  = 64
  special = true
}

# OpenAI API Key (SecureString)
resource "aws_ssm_parameter" "openai_api_key" {
  count = var.openai_api_key != "" ? 1 : 0

  name  = "/${var.name_prefix}/api-keys/openai"
  type  = "SecureString"
  value = var.openai_api_key

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-openai-api-key"
  })
}

# Anthropic API Key (SecureString)
resource "aws_ssm_parameter" "anthropic_api_key" {
  count = var.anthropic_api_key != "" ? 1 : 0

  name  = "/${var.name_prefix}/api-keys/anthropic"
  type  = "SecureString"
  value = var.anthropic_api_key

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-anthropic-api-key"
  })
}

# Google API Key (SecureString)
resource "aws_ssm_parameter" "google_api_key" {
  count = var.google_api_key != "" ? 1 : 0

  name  = "/${var.name_prefix}/api-keys/google"
  type  = "SecureString"
  value = var.google_api_key

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-google-api-key"
  })
}

# Application Configuration Parameters (String)
resource "aws_ssm_parameter" "app_config" {
  for_each = {
    "log-level"              = "info"
    "debug"                  = "false"
    "access-token-expire"    = "30"
    "cors-origins"           = "*"
    "max-request-size"       = "10485760"  # 10MB
    "rate-limit-requests"    = "100"
    "rate-limit-window"      = "60"
  }

  name  = "/${var.name_prefix}/app/config/${each.key}"
  type  = "String"
  value = each.value

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-config-${each.key}"
  })
}

# Environment-specific parameters
resource "aws_ssm_parameter" "environment" {
  name  = "/${var.name_prefix}/app/environment"
  type  = "String"
  value = var.environment

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-environment"
  })
}

# IAM Role for ECS tasks to access SSM parameters
resource "aws_iam_role" "ecs_task_execution_role" {
  name = "${var.name_prefix}-ecs-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Attach AWS managed policy for ECS task execution
resource "aws_iam_role_policy_attachment" "ecs_task_execution_role_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Custom policy for SSM parameter access
resource "aws_iam_role_policy" "ecs_ssm_policy" {
  name = "${var.name_prefix}-ecs-ssm-policy"
  role = aws_iam_role.ecs_task_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:GetParametersByPath"
        ]
        Resource = [
          "arn:aws:ssm:*:*:parameter/${var.name_prefix}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt"
        ]
        Resource = "*"
        Condition = {
          StringEquals = {
            "kms:ViaService" = "ssm.${data.aws_region.current.name}.amazonaws.com"
          }
        }
      }
    ]
  })
}

# Data source for current region
data "aws_region" "current" {}
