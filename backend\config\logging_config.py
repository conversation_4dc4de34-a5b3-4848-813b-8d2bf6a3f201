"""
Logging Configuration for Expendra Backend
Provides structured logging with CloudWatch integration
"""

import os
import sys
import json
import logging
import logging.config
from datetime import datetime
from typing import Dict, Any


class CloudWatchFormatter(logging.Formatter):
    """Custom formatter for CloudWatch logs with structured JSON output"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON for CloudWatch"""
        
        # Create base log entry
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields from the log record
        extra_fields = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                extra_fields[key] = value
        
        if extra_fields:
            log_entry['extra'] = extra_fields
        
        return json.dumps(log_entry, default=str)


class LocalFormatter(logging.Formatter):
    """Human-readable formatter for local development"""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )


def setup_logging(
    log_level: str = "INFO",
    environment: str = "prod",
    service_name: str = "expendra-backend"
) -> None:
    """
    Setup logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        environment: Environment name (dev, staging, prod)
        service_name: Name of the service for log identification
    """
    
    # Determine if we're running in AWS (ECS/Lambda) or locally
    is_aws_environment = (
        os.getenv('AWS_EXECUTION_ENV') is not None or
        os.getenv('ECS_CONTAINER_METADATA_URI') is not None or
        environment in ['staging', 'prod']
    )
    
    # Configure logging based on environment
    if is_aws_environment:
        # Use JSON formatter for CloudWatch
        formatter = CloudWatchFormatter()
        handler = logging.StreamHandler(sys.stdout)
    else:
        # Use human-readable formatter for local development
        formatter = LocalFormatter()
        handler = logging.StreamHandler(sys.stdout)
    
    handler.setFormatter(formatter)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        handlers=[handler],
        force=True
    )
    
    # Configure specific loggers
    loggers_config = {
        'uvicorn': logging.WARNING,
        'uvicorn.access': logging.INFO if environment == 'dev' else logging.WARNING,
        'fastapi': logging.INFO,
        'sqlalchemy.engine': logging.WARNING,
        'boto3': logging.WARNING,
        'botocore': logging.WARNING,
        'urllib3': logging.WARNING,
        'requests': logging.WARNING,
        'httpx': logging.WARNING,
        'playwright': logging.WARNING,
    }
    
    for logger_name, level in loggers_config.items():
        logging.getLogger(logger_name).setLevel(level)
    
    # Add service name to all log records
    class ServiceNameFilter(logging.Filter):
        def filter(self, record):
            record.service = service_name
            record.environment = environment
            return True
    
    # Add filter to root logger
    logging.getLogger().addFilter(ServiceNameFilter())
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info(
        "Logging configured",
        extra={
            'log_level': log_level,
            'environment': environment,
            'service_name': service_name,
            'is_aws_environment': is_aws_environment
        }
    )


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


def log_request_response(
    logger: logging.Logger,
    method: str,
    url: str,
    status_code: int,
    response_time: float,
    user_id: str = None,
    request_id: str = None
) -> None:
    """
    Log HTTP request/response information
    
    Args:
        logger: Logger instance
        method: HTTP method
        url: Request URL
        status_code: HTTP status code
        response_time: Response time in seconds
        user_id: User ID if authenticated
        request_id: Request ID for tracing
    """
    logger.info(
        f"{method} {url} - {status_code}",
        extra={
            'http_method': method,
            'url': url,
            'status_code': status_code,
            'response_time': response_time,
            'user_id': user_id,
            'request_id': request_id,
            'event_type': 'http_request'
        }
    )


def log_database_operation(
    logger: logging.Logger,
    operation: str,
    table: str,
    duration: float,
    rows_affected: int = None,
    user_id: str = None
) -> None:
    """
    Log database operation information
    
    Args:
        logger: Logger instance
        operation: Database operation (SELECT, INSERT, UPDATE, DELETE)
        table: Table name
        duration: Operation duration in seconds
        rows_affected: Number of rows affected
        user_id: User ID performing the operation
    """
    logger.info(
        f"Database {operation} on {table}",
        extra={
            'db_operation': operation,
            'table': table,
            'duration': duration,
            'rows_affected': rows_affected,
            'user_id': user_id,
            'event_type': 'database_operation'
        }
    )


def log_ai_operation(
    logger: logging.Logger,
    provider: str,
    model: str,
    operation: str,
    tokens_used: int = None,
    cost: float = None,
    duration: float = None,
    user_id: str = None
) -> None:
    """
    Log AI/LLM operation information
    
    Args:
        logger: Logger instance
        provider: AI provider (openai, anthropic, etc.)
        model: Model name
        operation: Operation type (completion, embedding, etc.)
        tokens_used: Number of tokens used
        cost: Estimated cost
        duration: Operation duration in seconds
        user_id: User ID
    """
    logger.info(
        f"AI operation: {provider}/{model} - {operation}",
        extra={
            'ai_provider': provider,
            'ai_model': model,
            'ai_operation': operation,
            'tokens_used': tokens_used,
            'cost': cost,
            'duration': duration,
            'user_id': user_id,
            'event_type': 'ai_operation'
        }
    )


# Initialize logging when module is imported
if __name__ != "__main__":
    # Get configuration from environment variables
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    environment = os.getenv('ENVIRONMENT', 'prod')
    service_name = os.getenv('SERVICE_NAME', 'expendra-backend')
    
    setup_logging(log_level, environment, service_name)
