<!DOCTYPE html>
<html>
<head>
    <title>Frontend-Backend Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🧪 Expendra Frontend-Backend Integration Test</h1>
    
    <div id="results"></div>
    
    <button onclick="testHealthEndpoint()">Test Health Endpoint</button>
    <button onclick="testResearchConnection()">Test Research Connection</button>
    <button onclick="testAllEndpoints()">Test All Endpoints</button>
    
    <script>
        const API_BASE_URL = 'https://d26l8pqbmembp6.cloudfront.net/api/v1';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        async function testHealthEndpoint() {
            addResult('Testing health endpoint...', 'info');
            try {
                const response = await fetch('https://d26l8pqbmembp6.cloudfront.net/api/health');
                const data = await response.json();
                if (response.ok) {
                    addResult(`✅ Health endpoint working: ${data.message}`, 'success');
                } else {
                    addResult(`❌ Health endpoint failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Health endpoint error: ${error.message}`, 'error');
            }
        }
        
        async function testResearchConnection() {
            addResult('Testing research connection endpoint...', 'info');
            try {
                const response = await fetch(`${API_BASE_URL}/research/test-connection-public`);
                const data = await response.json();
                if (response.ok) {
                    addResult(`✅ Research connection working: ${data.message}`, 'success');
                    addResult(`📊 LLM Service Status: ${data.llm_service.status} (${data.llm_service.provider})`, 'info');
                } else {
                    addResult(`❌ Research connection failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Research connection error: ${error.message}`, 'error');
            }
        }
        
        async function testAllEndpoints() {
            addResult('🚀 Starting comprehensive integration test...', 'info');
            await testHealthEndpoint();
            await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
            await testResearchConnection();
            addResult('✨ Integration test completed!', 'success');
        }
        
        // Auto-run basic test on page load
        window.onload = function() {
            addResult('🎯 Frontend-Backend Integration Test Ready', 'info');
            addResult('Click buttons above to test different endpoints', 'info');
        };
    </script>
</body>
</html>
