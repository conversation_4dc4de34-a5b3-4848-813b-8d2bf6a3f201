-- Expendra Database Initialization Script
-- This script sets up the initial database structure for local development

-- Create database if it doesn't exist (PostgreSQL)
-- Note: The database is already created by the POSTGRES_DB environment variable

-- Set timezone
SET timezone = 'UTC';

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant necessary permissions to the application user
GRANT ALL PRIVILEGES ON DATABASE expendra TO expendra_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO expendra_user;

-- Create a simple health check table for monitoring
CREATE TABLE IF NOT EXISTS health_check (
    id SERIAL PRIMARY KEY,
    status VARCHAR(50) NOT NULL DEFAULT 'healthy',
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert initial health check record
INSERT INTO health_check (status) VALUES ('healthy') ON CONFLICT DO NOTHING;

-- Grant permissions on the health check table
GRANT ALL PRIVILEGES ON TABLE health_check TO expendra_user;
GRANT ALL PRIVILEGES ON SEQUENCE health_check_id_seq TO expendra_user;
