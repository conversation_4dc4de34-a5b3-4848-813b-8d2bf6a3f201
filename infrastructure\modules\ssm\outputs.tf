output "ecs_task_execution_role_arn" {
  description = "ARN of the ECS task execution role"
  value       = aws_iam_role.ecs_task_execution_role.arn
}

output "database_url_parameter_name" {
  description = "SSM parameter name for database URL"
  value       = aws_ssm_parameter.database_url.name
}

output "database_password_parameter_name" {
  description = "SSM parameter name for database password"
  value       = aws_ssm_parameter.database_password.name
}

output "secret_key_parameter_name" {
  description = "SSM parameter name for application secret key"
  value       = aws_ssm_parameter.secret_key.name
}

output "openai_api_key_parameter_name" {
  description = "SSM parameter name for OpenAI API key"
  value       = length(aws_ssm_parameter.openai_api_key) > 0 ? aws_ssm_parameter.openai_api_key[0].name : ""
}

output "anthropic_api_key_parameter_name" {
  description = "SSM parameter name for Anthropic API key"
  value       = length(aws_ssm_parameter.anthropic_api_key) > 0 ? aws_ssm_parameter.anthropic_api_key[0].name : ""
}

output "google_api_key_parameter_name" {
  description = "SSM parameter name for Google API key"
  value       = length(aws_ssm_parameter.google_api_key) > 0 ? aws_ssm_parameter.google_api_key[0].name : ""
}

output "parameter_prefix" {
  description = "Prefix for all SSM parameters"
  value       = "/${var.name_prefix}"
}
