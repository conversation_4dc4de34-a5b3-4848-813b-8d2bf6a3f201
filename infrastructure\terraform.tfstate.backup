{"version": 4, "terraform_version": "1.12.2", "serial": 93, "lineage": "78d947da-ceee-6bae-79d1-3974d8c2cf81", "outputs": {"alb_dns_name": {"value": "expendra-prod-alb-**********.us-east-1.elb.amazonaws.com", "type": "string"}, "application_url": {"value": "https://d26l8pqbmembp6.cloudfront.net", "type": "string"}, "backend_api_url": {"value": "https://d26l8pqbmembp6.cloudfront.net/api", "type": "string"}, "backend_ecr_repository_url": {"value": "************.dkr.ecr.us-east-1.amazonaws.com/expendra-backend", "type": "string"}, "cloudfront_distribution_id": {"value": "E2XDJ0AMBOPN3U", "type": "string"}, "cloudfront_domain_name": {"value": "d26l8pqbmembp6.cloudfront.net", "type": "string"}, "database_endpoint": {"value": "expendra-prod-database.c2las2g82og9.us-east-1.rds.amazonaws.com:5432", "type": "string", "sensitive": true}, "database_port": {"value": 5432, "type": "number"}, "ecs_cluster_name": {"value": "expendra-prod-cluster", "type": "string"}, "frontend_ecr_repository_url": {"value": "************.dkr.ecr.us-east-1.amazonaws.com/expendra-frontend", "type": "string"}, "private_subnet_ids": {"value": ["subnet-0102115d12ff8a351", "subnet-074fc3f2b9eb5c618", "subnet-0f0676440188ece3c"], "type": ["tuple", ["string", "string", "string"]]}, "public_subnet_ids": {"value": ["subnet-072c7f3424118b4ea", "subnet-0d0d900cc223caf27", "subnet-03b6b3fd2924864cc"], "type": ["tuple", ["string", "string", "string"]]}, "static_assets_bucket_id": {"value": "expendra-prod-static-assets-do3sg8op", "type": "string"}, "vpc_id": {"value": "vpc-09b56c429ea6ab2aa", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_availability_zones", "name": "available", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["us-east-1-zg-1"], "id": "us-east-1", "names": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "state": "available", "timeouts": null, "zone_ids": ["use1-az4", "use1-az6", "use1-az1", "use1-az2", "use1-az3", "use1-az5"]}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/Chris", "id": "************", "user_id": "AIDATOAAUQWYC6ACR7AE7"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "managed", "type": "aws_s3_bucket_policy", "name": "static_assets_cloudfront", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "expendra-prod-static-assets-do3sg8op", "id": "expendra-prod-static-assets-do3sg8op", "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Condition\":{\"StringEquals\":{\"AWS:SourceArn\":\"arn:aws:cloudfront::************:distribution/E2XDJ0AMBOPN3U\"}},\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"cloudfront.amazonaws.com\"},\"Resource\":\"arn:aws:s3:::expendra-prod-static-assets-do3sg8op/*\",\"Sid\":\"AllowCloudFrontServicePrincipal\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.cloudfront.aws_cloudfront_distribution.main", "module.cloudfront.aws_cloudfront_origin_access_control.main", "module.s3.aws_s3_bucket.static_assets", "module.s3.aws_s3_bucket.terraform_state", "module.s3.aws_s3_bucket_public_access_block.static_assets", "module.s3.aws_s3_bucket_public_access_block.terraform_state", "module.s3.aws_s3_bucket_server_side_encryption_configuration.static_assets", "module.s3.aws_s3_bucket_server_side_encryption_configuration.terraform_state", "module.s3.aws_s3_bucket_versioning.static_assets", "module.s3.aws_s3_bucket_versioning.terraform_state", "module.s3.random_string.bucket_suffix", "module.security_groups.aws_security_group.alb", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}]}, {"mode": "managed", "type": "random_password", "name": "db_password", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 3, "attributes": {"bcrypt_hash": "$2a$10$KjmosjDX2srPk8Roz3h3EOzSJuvvUp/PM7qJzEGoyAHv.fS30C6fO", "id": "none", "keepers": null, "length": 16, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": null, "result": "<3tQEucnl:q<nREL", "special": true, "upper": true}, "sensitive_attributes": [[{"type": "get_attr", "value": "bcrypt_hash"}], [{"type": "get_attr", "value": "result"}]], "identity_schema_version": 0}]}, {"module": "module.alb", "mode": "managed", "type": "aws_lb", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_logs": [{"bucket": "", "enabled": false, "prefix": ""}], "arn": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/expendra-prod-alb/8953aa6d7b05b5ed", "arn_suffix": "app/expendra-prod-alb/8953aa6d7b05b5ed", "client_keep_alive": 3600, "connection_logs": [{"bucket": "", "enabled": false, "prefix": ""}], "customer_owned_ipv4_pool": "", "desync_mitigation_mode": "defensive", "dns_name": "expendra-prod-alb-**********.us-east-1.elb.amazonaws.com", "dns_record_client_routing_policy": null, "drop_invalid_header_fields": false, "enable_cross_zone_load_balancing": true, "enable_deletion_protection": false, "enable_http2": true, "enable_tls_version_and_cipher_suite_headers": false, "enable_waf_fail_open": false, "enable_xff_client_port": false, "enable_zonal_shift": false, "enforce_security_group_inbound_rules_on_private_link_traffic": "", "id": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/expendra-prod-alb/8953aa6d7b05b5ed", "idle_timeout": 60, "internal": false, "ip_address_type": "ipv4", "ipam_pools": [], "load_balancer_type": "application", "minimum_load_balancer_capacity": [], "name": "expendra-prod-alb", "name_prefix": "", "preserve_host_header": false, "security_groups": ["sg-0f22269bbe5e2e939"], "subnet_mapping": [{"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-03b6b3fd2924864cc"}, {"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-072c7f3424118b4ea"}, {"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-0d0d900cc223caf27"}], "subnets": ["subnet-03b6b3fd2924864cc", "subnet-072c7f3424118b4ea", "subnet-0d0d900cc223caf27"], "tags": {"Environment": "prod", "Name": "expendra-prod-alb", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-alb", "Project": "expendra"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa", "xff_header_processing_mode": "append", "zone_id": "Z35SXDOTRQ7X7K"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.security_groups.aws_security_group.alb", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}]}, {"module": "module.alb", "mode": "managed", "type": "aws_lb_listener", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alpn_policy": null, "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/expendra-prod-alb/8953aa6d7b05b5ed/692449cda067759c", "certificate_arn": null, "default_action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/expendra-prod-frontend-tg/dc55738fd212d397", "type": "forward"}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/expendra-prod-alb/8953aa6d7b05b5ed/692449cda067759c", "load_balancer_arn": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/expendra-prod-alb/8953aa6d7b05b5ed", "mutual_authentication": [], "port": 80, "protocol": "HTTP", "routing_http_request_x_amzn_mtls_clientcert_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_issuer_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_leaf_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_subject_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_validity_header_name": null, "routing_http_request_x_amzn_tls_cipher_suite_header_name": null, "routing_http_request_x_amzn_tls_version_header_name": null, "routing_http_response_access_control_allow_credentials_header_value": "", "routing_http_response_access_control_allow_headers_header_value": "", "routing_http_response_access_control_allow_methods_header_value": "", "routing_http_response_access_control_allow_origin_header_value": "", "routing_http_response_access_control_expose_headers_header_value": "", "routing_http_response_access_control_max_age_header_value": "", "routing_http_response_content_security_policy_header_value": "", "routing_http_response_server_enabled": true, "routing_http_response_strict_transport_security_header_value": "", "routing_http_response_x_content_type_options_header_value": "", "routing_http_response_x_frame_options_header_value": "", "ssl_policy": "", "tags": {}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "Expendra"}, "tcp_idle_timeout_seconds": null, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.alb.aws_lb_target_group.frontend", "module.security_groups.aws_security_group.alb", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}]}, {"module": "module.alb", "mode": "managed", "type": "aws_lb_listener_rule", "name": "api", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/expendra-prod-backend-tg/08be7f4d4a8d2204", "type": "forward"}], "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/expendra-prod-alb/8953aa6d7b05b5ed/692449cda067759c/ba8d2ba1cd12312c", "condition": [{"host_header": [], "http_header": [], "http_request_method": [], "path_pattern": [{"values": ["/api/*"]}], "query_string": [], "source_ip": []}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/expendra-prod-alb/8953aa6d7b05b5ed/692449cda067759c/ba8d2ba1cd12312c", "listener_arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/expendra-prod-alb/8953aa6d7b05b5ed/692449cda067759c", "priority": 100, "tags": {}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "Expendra"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.alb.aws_lb_listener.main", "module.alb.aws_lb_target_group.backend", "module.alb.aws_lb_target_group.frontend", "module.security_groups.aws_security_group.alb", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}]}, {"module": "module.alb", "mode": "managed", "type": "aws_lb_listener_rule", "name": "health", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/expendra-prod-backend-tg/08be7f4d4a8d2204", "type": "forward"}], "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/expendra-prod-alb/8953aa6d7b05b5ed/692449cda067759c/14bf46e06552f96b", "condition": [{"host_header": [], "http_header": [], "http_request_method": [], "path_pattern": [{"values": ["/health"]}], "query_string": [], "source_ip": []}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/expendra-prod-alb/8953aa6d7b05b5ed/692449cda067759c/14bf46e06552f96b", "listener_arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/expendra-prod-alb/8953aa6d7b05b5ed/692449cda067759c", "priority": 200, "tags": {}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "Expendra"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.alb.aws_lb_listener.main", "module.alb.aws_lb_target_group.backend", "module.alb.aws_lb_target_group.frontend", "module.security_groups.aws_security_group.alb", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}]}, {"module": "module.alb", "mode": "managed", "type": "aws_lb_target_group", "name": "backend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/expendra-prod-backend-tg/08be7f4d4a8d2204", "arn_suffix": "targetgroup/expendra-prod-backend-tg/08be7f4d4a8d2204", "connection_termination": null, "deregistration_delay": "300", "health_check": [{"enabled": true, "healthy_threshold": 2, "interval": 30, "matcher": "200", "path": "/health", "port": "traffic-port", "protocol": "HTTP", "timeout": 5, "unhealthy_threshold": 2}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/expendra-prod-backend-tg/08be7f4d4a8d2204", "ip_address_type": "ipv4", "lambda_multi_value_headers_enabled": false, "load_balancer_arns": ["arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/expendra-prod-alb/8953aa6d7b05b5ed"], "load_balancing_algorithm_type": "round_robin", "load_balancing_anomaly_mitigation": "off", "load_balancing_cross_zone_enabled": "use_load_balancer_configuration", "name": "expendra-prod-backend-tg", "name_prefix": "", "port": 8000, "preserve_client_ip": null, "protocol": "HTTP", "protocol_version": "HTTP1", "proxy_protocol_v2": false, "slow_start": 0, "stickiness": [{"cookie_duration": 86400, "cookie_name": "", "enabled": false, "type": "lb_cookie"}], "tags": {"Environment": "prod", "Name": "expendra-prod-backend-tg", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-backend-tg", "Project": "expendra"}, "target_failover": [{"on_deregistration": null, "on_unhealthy": null}], "target_group_health": [{"dns_failover": [{"minimum_healthy_targets_count": "1", "minimum_healthy_targets_percentage": "off"}], "unhealthy_state_routing": [{"minimum_healthy_targets_count": 1, "minimum_healthy_targets_percentage": "off"}]}], "target_health_state": [{"enable_unhealthy_connection_termination": null, "unhealthy_draining_interval": null}], "target_type": "ip", "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.vpc.aws_vpc.main"]}]}, {"module": "module.alb", "mode": "managed", "type": "aws_lb_target_group", "name": "frontend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/expendra-prod-frontend-tg/dc55738fd212d397", "arn_suffix": "targetgroup/expendra-prod-frontend-tg/dc55738fd212d397", "connection_termination": null, "deregistration_delay": "300", "health_check": [{"enabled": true, "healthy_threshold": 2, "interval": 30, "matcher": "200", "path": "/", "port": "traffic-port", "protocol": "HTTP", "timeout": 5, "unhealthy_threshold": 2}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/expendra-prod-frontend-tg/dc55738fd212d397", "ip_address_type": "ipv4", "lambda_multi_value_headers_enabled": false, "load_balancer_arns": ["arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/expendra-prod-alb/8953aa6d7b05b5ed"], "load_balancing_algorithm_type": "round_robin", "load_balancing_anomaly_mitigation": "off", "load_balancing_cross_zone_enabled": "use_load_balancer_configuration", "name": "expendra-prod-frontend-tg", "name_prefix": "", "port": 80, "preserve_client_ip": null, "protocol": "HTTP", "protocol_version": "HTTP1", "proxy_protocol_v2": false, "slow_start": 0, "stickiness": [{"cookie_duration": 86400, "cookie_name": "", "enabled": false, "type": "lb_cookie"}], "tags": {"Environment": "prod", "Name": "expendra-prod-frontend-tg", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-frontend-tg", "Project": "expendra"}, "target_failover": [{"on_deregistration": null, "on_unhealthy": null}], "target_group_health": [{"dns_failover": [{"minimum_healthy_targets_count": "1", "minimum_healthy_targets_percentage": "off"}], "unhealthy_state_routing": [{"minimum_healthy_targets_count": 1, "minimum_healthy_targets_percentage": "off"}]}], "target_health_state": [{"enable_unhealthy_connection_termination": null, "unhealthy_draining_interval": null}], "target_type": "ip", "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.vpc.aws_vpc.main"]}]}, {"module": "module.cloudfront", "mode": "managed", "type": "aws_cloudfront_distribution", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"aliases": [], "arn": "arn:aws:cloudfront::************:distribution/E2XDJ0AMBOPN3U", "caller_reference": "terraform-2025062917172769850000000e", "comment": "expendra prod distribution", "continuous_deployment_policy_id": "", "custom_error_response": [{"error_caching_min_ttl": 0, "error_code": 403, "response_code": 200, "response_page_path": "/index.html"}, {"error_caching_min_ttl": 0, "error_code": 404, "response_code": 200, "response_page_path": "/index.html"}], "default_cache_behavior": [{"allowed_methods": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"], "cache_policy_id": "", "cached_methods": ["GET", "HEAD"], "compress": false, "default_ttl": 3600, "field_level_encryption_id": "", "forwarded_values": [{"cookies": [{"forward": "none", "whitelisted_names": []}], "headers": [], "query_string": false, "query_string_cache_keys": []}], "function_association": [], "grpc_config": [{"enabled": false}], "lambda_function_association": [], "max_ttl": 86400, "min_ttl": 0, "origin_request_policy_id": "", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "S3-expendra-prod-static-assets-do3sg8op", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "redirect-to-https"}], "default_root_object": "index.html", "domain_name": "d26l8pqbmembp6.cloudfront.net", "enabled": true, "etag": "E23K82O448NENQ", "hosted_zone_id": "Z2FDTNDATAQYW2", "http_version": "http2", "id": "E2XDJ0AMBOPN3U", "in_progress_validation_batches": 0, "is_ipv6_enabled": true, "last_modified_time": "2025-06-29 18:57:22.158 +0000 UTC", "logging_config": [], "ordered_cache_behavior": [{"allowed_methods": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"], "cache_policy_id": "", "cached_methods": ["GET", "HEAD", "OPTIONS"], "compress": false, "default_ttl": 0, "field_level_encryption_id": "", "forwarded_values": [{"cookies": [{"forward": "all", "whitelisted_names": []}], "headers": ["*"], "query_string": true, "query_string_cache_keys": []}], "function_association": [], "grpc_config": [{"enabled": false}], "lambda_function_association": [], "max_ttl": 0, "min_ttl": 0, "origin_request_policy_id": "", "path_pattern": "/api/*", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "ALB-expendra-prod", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "redirect-to-https"}, {"allowed_methods": ["GET", "HEAD", "OPTIONS"], "cache_policy_id": "", "cached_methods": ["GET", "HEAD", "OPTIONS"], "compress": false, "default_ttl": 0, "field_level_encryption_id": "", "forwarded_values": [{"cookies": [{"forward": "none", "whitelisted_names": []}], "headers": [], "query_string": false, "query_string_cache_keys": []}], "function_association": [], "grpc_config": [{"enabled": false}], "lambda_function_association": [], "max_ttl": 0, "min_ttl": 0, "origin_request_policy_id": "", "path_pattern": "/health", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "ALB-expendra-prod", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "redirect-to-https"}], "origin": [{"connection_attempts": 3, "connection_timeout": 10, "custom_header": [], "custom_origin_config": [{"http_port": 80, "https_port": 443, "origin_keepalive_timeout": 5, "origin_protocol_policy": "http-only", "origin_read_timeout": 30, "origin_ssl_protocols": ["TLSv1.2"]}], "domain_name": "expendra-prod-alb-**********.us-east-1.elb.amazonaws.com", "origin_access_control_id": "", "origin_id": "ALB-expendra-prod", "origin_path": "", "origin_shield": [], "s3_origin_config": [], "vpc_origin_config": []}, {"connection_attempts": 3, "connection_timeout": 10, "custom_header": [], "custom_origin_config": [], "domain_name": "expendra-prod-static-assets-do3sg8op.s3.amazonaws.com", "origin_access_control_id": "EB1TSSHASY0DV", "origin_id": "S3-expendra-prod-static-assets-do3sg8op", "origin_path": "", "origin_shield": [], "s3_origin_config": [], "vpc_origin_config": []}], "origin_group": [], "price_class": "PriceClass_100", "restrictions": [{"geo_restriction": [{"locations": [], "restriction_type": "none"}]}], "retain_on_delete": false, "staging": false, "status": "Deployed", "tags": {"Environment": "prod", "Name": "expendra-prod-cloudfront", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-cloudfront", "Project": "expendra"}, "trusted_key_groups": [{"enabled": false, "items": []}], "trusted_signers": [{"enabled": false, "items": []}], "viewer_certificate": [{"acm_certificate_arn": "", "cloudfront_default_certificate": true, "iam_certificate_id": "", "minimum_protocol_version": "TLSv1", "ssl_support_method": ""}], "wait_for_deployment": true, "web_acl_id": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.cloudfront.aws_cloudfront_origin_access_control.main", "module.s3.aws_s3_bucket.static_assets", "module.s3.random_string.bucket_suffix", "module.security_groups.aws_security_group.alb", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}]}, {"module": "module.cloudfront", "mode": "managed", "type": "aws_cloudfront_origin_access_control", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:cloudfront::************:origin-access-control/EB1TSSHASY0DV", "description": "OAC for expendra prod", "etag": "ETVPDKIKX0DER", "id": "EB1TSSHASY0DV", "name": "expendra-prod-oac", "origin_access_control_origin_type": "s3", "signing_behavior": "always", "signing_protocol": "sigv4"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"module": "module.database", "mode": "managed", "type": "aws_db_instance", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"address": "expendra-prod-database.c2las2g82og9.us-east-1.rds.amazonaws.com", "allocated_storage": 20, "allow_major_version_upgrade": null, "apply_immediately": false, "arn": "arn:aws:rds:us-east-1:************:db:expendra-prod-database", "auto_minor_version_upgrade": true, "availability_zone": "us-east-1a", "backup_retention_period": 7, "backup_target": "region", "backup_window": "03:00-04:00", "blue_green_update": [], "ca_cert_identifier": "rds-ca-rsa2048-g1", "character_set_name": "", "copy_tags_to_snapshot": false, "custom_iam_instance_profile": "", "customer_owned_ip_enabled": false, "database_insights_mode": "standard", "db_name": "expendra", "db_subnet_group_name": "expendra-prod-db-subnet-group", "dedicated_log_volume": false, "delete_automated_backups": true, "deletion_protection": true, "domain": "", "domain_auth_secret_arn": "", "domain_dns_ips": [], "domain_fqdn": "", "domain_iam_role_name": "", "domain_ou": "", "enabled_cloudwatch_logs_exports": [], "endpoint": "expendra-prod-database.c2las2g82og9.us-east-1.rds.amazonaws.com:5432", "engine": "postgres", "engine_lifecycle_support": "open-source-rds-extended-support", "engine_version": "15.7", "engine_version_actual": "15.7", "final_snapshot_identifier": "expendra-prod-final-snapshot-2025-06-29-1857", "hosted_zone_id": "Z2R2ITUGPM61AM", "iam_database_authentication_enabled": false, "id": "db-FGGCQV4KQJCZQXKROVEL6EBDTM", "identifier": "expendra-prod-database", "identifier_prefix": "", "instance_class": "db.t3.micro", "iops": 0, "kms_key_id": "arn:aws:kms:us-east-1:************:key/00b7bb71-83fa-4740-a383-5d7e6fcd7017", "latest_restorable_time": "2025-06-29T18:54:30Z", "license_model": "postgresql-license", "listener_endpoint": [], "maintenance_window": "sun:04:00-sun:05:00", "manage_master_user_password": null, "master_user_secret": [], "master_user_secret_kms_key_id": null, "max_allocated_storage": 40, "monitoring_interval": 60, "monitoring_role_arn": "arn:aws:iam::************:role/expendra-prod-rds-monitoring-role", "multi_az": false, "nchar_character_set_name": "", "network_type": "IPV4", "option_group_name": "default:postgres-15", "parameter_group_name": "expendra-prod-db-params", "password": "<3tQEucnl:q<nREL", "password_wo": null, "password_wo_version": null, "performance_insights_enabled": true, "performance_insights_kms_key_id": "arn:aws:kms:us-east-1:************:key/00b7bb71-83fa-4740-a383-5d7e6fcd7017", "performance_insights_retention_period": 7, "port": 5432, "publicly_accessible": false, "replica_mode": "", "replicas": [], "replicate_source_db": "", "resource_id": "db-FGGCQV4KQJCZQXKROVEL6EBDTM", "restore_to_point_in_time": [], "s3_import": [], "skip_final_snapshot": false, "snapshot_identifier": null, "status": "available", "storage_encrypted": true, "storage_throughput": 0, "storage_type": "gp2", "tags": {"Environment": "prod", "Name": "expendra-prod-database", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-database", "Project": "expendra"}, "timeouts": null, "timezone": "", "upgrade_storage_config": null, "username": "expendra_user", "vpc_security_group_ids": ["sg-0f34a629067e0bad2"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "password"}], [{"type": "get_attr", "value": "password_wo"}]], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInVwZGF0ZSI6NDgwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMiJ9", "dependencies": ["data.aws_availability_zones.available", "module.database.aws_db_parameter_group.main", "module.database.aws_db_subnet_group.main", "module.database.aws_iam_role.rds_monitoring", "module.security_groups.aws_security_group.alb", "module.security_groups.aws_security_group.database", "module.security_groups.aws_security_group.ecs_backend", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.main", "random_password.db_password"]}]}, {"module": "module.database", "mode": "managed", "type": "aws_db_parameter_group", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:us-east-1:************:pg:expendra-prod-db-params", "description": "Managed by Terraform", "family": "postgres15", "id": "expendra-prod-db-params", "name": "expendra-prod-db-params", "name_prefix": "", "parameter": [{"apply_method": "immediate", "name": "log_min_duration_statement", "value": "1000"}, {"apply_method": "immediate", "name": "log_statement", "value": "all"}], "skip_destroy": false, "tags": {"Environment": "prod", "Name": "expendra-prod-db-params", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-db-params", "Project": "expendra"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"module": "module.database", "mode": "managed", "type": "aws_db_subnet_group", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:us-east-1:************:subgrp:expendra-prod-db-subnet-group", "description": "Managed by Terraform", "id": "expendra-prod-db-subnet-group", "name": "expendra-prod-db-subnet-group", "name_prefix": "", "subnet_ids": ["subnet-0102115d12ff8a351", "subnet-074fc3f2b9eb5c618", "subnet-0f0676440188ece3c"], "supported_network_types": ["IPV4"], "tags": {"Environment": "prod", "Name": "expendra-prod-db-subnet-group", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-db-subnet-group", "Project": "expendra"}, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.main"]}]}, {"module": "module.database", "mode": "managed", "type": "aws_iam_role", "name": "rds_monitoring", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/expendra-prod-rds-monitoring-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"monitoring.rds.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-29T17:13:47Z", "description": "", "force_detach_policies": false, "id": "expendra-prod-rds-monitoring-role", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"], "max_session_duration": 3600, "name": "expendra-prod-rds-monitoring-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "prod", "Name": "expendra-prod-rds-monitoring-role", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-rds-monitoring-role", "Project": "expendra"}, "unique_id": "AROATOAAUQWYAIMF5A7RW"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"module": "module.database", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "rds_monitoring", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "expendra-prod-rds-monitoring-role-20250629171356237300000001", "policy_arn": "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole", "role": "expendra-prod-rds-monitoring-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.database.aws_iam_role.rds_monitoring"]}]}, {"module": "module.ecr", "mode": "managed", "type": "aws_ecr_lifecycle_policy", "name": "backend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "expendra-backend", "policy": "{\"rules\":[{\"action\":{\"type\":\"expire\"},\"description\":\"Keep last 10 images\",\"rulePriority\":1,\"selection\":{\"countNumber\":10,\"countType\":\"imageCountMoreThan\",\"tagPrefixList\":[\"v\"],\"tagStatus\":\"tagged\"}},{\"action\":{\"type\":\"expire\"},\"description\":\"Delete untagged images older than 1 day\",\"rulePriority\":2,\"selection\":{\"countNumber\":1,\"countType\":\"sinceImagePushed\",\"countUnit\":\"days\",\"tagStatus\":\"untagged\"}}]}", "registry_id": "************", "repository": "expendra-backend"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.ecr.aws_ecr_repository.backend"]}]}, {"module": "module.ecr", "mode": "managed", "type": "aws_ecr_lifecycle_policy", "name": "frontend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "expendra-frontend", "policy": "{\"rules\":[{\"action\":{\"type\":\"expire\"},\"description\":\"Keep last 10 images\",\"rulePriority\":1,\"selection\":{\"countNumber\":10,\"countType\":\"imageCountMoreThan\",\"tagPrefixList\":[\"v\"],\"tagStatus\":\"tagged\"}},{\"action\":{\"type\":\"expire\"},\"description\":\"Delete untagged images older than 1 day\",\"rulePriority\":2,\"selection\":{\"countNumber\":1,\"countType\":\"sinceImagePushed\",\"countUnit\":\"days\",\"tagStatus\":\"untagged\"}}]}", "registry_id": "************", "repository": "expendra-frontend"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.ecr.aws_ecr_repository.frontend"]}]}, {"module": "module.ecr", "mode": "managed", "type": "aws_ecr_repository", "name": "backend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecr:us-east-1:************:repository/expendra-backend", "encryption_configuration": [{"encryption_type": "AES256", "kms_key": ""}], "force_delete": null, "id": "expendra-backend", "image_scanning_configuration": [{"scan_on_push": true}], "image_tag_mutability": "MUTABLE", "name": "expendra-backend", "registry_id": "************", "repository_url": "************.dkr.ecr.us-east-1.amazonaws.com/expendra-backend", "tags": {"Environment": "prod", "Name": "expendra-backend-ecr", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-backend-ecr", "Project": "expendra"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxMjAwMDAwMDAwMDAwfX0="}]}, {"module": "module.ecr", "mode": "managed", "type": "aws_ecr_repository", "name": "frontend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecr:us-east-1:************:repository/expendra-frontend", "encryption_configuration": [{"encryption_type": "AES256", "kms_key": ""}], "force_delete": null, "id": "expendra-frontend", "image_scanning_configuration": [{"scan_on_push": true}], "image_tag_mutability": "MUTABLE", "name": "expendra-frontend", "registry_id": "************", "repository_url": "************.dkr.ecr.us-east-1.amazonaws.com/expendra-frontend", "tags": {"Environment": "prod", "Name": "expendra-frontend-ecr", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-frontend-ecr", "Project": "expendra"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxMjAwMDAwMDAwMDAwfX0="}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "backend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/aws/ecs/expendra-prod-backend", "id": "/aws/ecs/expendra-prod-backend", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/ecs/expendra-prod-backend", "name_prefix": "", "retention_in_days": 14, "skip_destroy": false, "tags": {"Environment": "prod", "Name": "expendra-prod-backend-logs", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-backend-logs", "Project": "expendra"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "ecs_exec", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/aws/ecs/expendra-prod-exec", "id": "/aws/ecs/expendra-prod-exec", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/ecs/expendra-prod-exec", "name_prefix": "", "retention_in_days": 7, "skip_destroy": false, "tags": {"Environment": "prod", "Name": "expendra-prod-ecs-exec-logs", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-ecs-exec-logs", "Project": "expendra"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "frontend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/aws/ecs/expendra-prod-frontend", "id": "/aws/ecs/expendra-prod-frontend", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/ecs/expendra-prod-frontend", "name_prefix": "", "retention_in_days": 14, "skip_destroy": false, "tags": {"Environment": "prod", "Name": "expendra-prod-frontend-logs", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-frontend-logs", "Project": "expendra"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_ecs_cluster", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:cluster/expendra-prod-cluster", "configuration": [{"execute_command_configuration": [{"kms_key_id": "", "log_configuration": [{"cloud_watch_encryption_enabled": false, "cloud_watch_log_group_name": "/aws/ecs/expendra-prod-exec", "s3_bucket_encryption_enabled": false, "s3_bucket_name": "", "s3_key_prefix": ""}], "logging": "OVERRIDE"}], "managed_storage_configuration": []}], "id": "arn:aws:ecs:us-east-1:************:cluster/expendra-prod-cluster", "name": "expendra-prod-cluster", "service_connect_defaults": [], "setting": [{"name": "containerInsights", "value": "disabled"}], "tags": {"Environment": "prod", "Name": "expendra-prod-cluster", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-cluster", "Project": "expendra"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.ecs.aws_cloudwatch_log_group.ecs_exec"]}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_ecs_service", "name": "backend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"alarms": [], "availability_zone_rebalancing": "DISABLED", "capacity_provider_strategy": [], "cluster": "arn:aws:ecs:us-east-1:************:cluster/expendra-prod-cluster", "deployment_circuit_breaker": [{"enable": false, "rollback": false}], "deployment_controller": [{"type": "ECS"}], "deployment_maximum_percent": 200, "deployment_minimum_healthy_percent": 100, "desired_count": 1, "enable_ecs_managed_tags": false, "enable_execute_command": false, "force_delete": null, "force_new_deployment": null, "health_check_grace_period_seconds": 0, "iam_role": "/aws-service-role/ecs.amazonaws.com/AWSServiceRoleForECS", "id": "arn:aws:ecs:us-east-1:************:service/expendra-prod-cluster/expendra-prod-backend", "launch_type": "FARGATE", "load_balancer": [{"container_name": "backend", "container_port": 8000, "elb_name": "", "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/expendra-prod-backend-tg/08be7f4d4a8d2204"}], "name": "expendra-prod-backend", "network_configuration": [{"assign_public_ip": false, "security_groups": ["sg-0bb654cbe7cab50ec"], "subnets": ["subnet-0102115d12ff8a351", "subnet-074fc3f2b9eb5c618", "subnet-0f0676440188ece3c"]}], "ordered_placement_strategy": [], "placement_constraints": [], "platform_version": "LATEST", "propagate_tags": "NONE", "scheduling_strategy": "REPLICA", "service_connect_configuration": [], "service_registries": [], "tags": {"Environment": "prod", "Name": "expendra-prod-backend-service", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-backend-service", "Project": "expendra"}, "task_definition": "arn:aws:ecs:us-east-1:************:task-definition/expendra-prod-backend:1", "timeouts": null, "triggers": {}, "volume_configuration": [], "vpc_lattice_configurations": [], "wait_for_steady_state": false}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb_target_group.backend", "module.ecr.aws_ecr_repository.backend", "module.ecs.aws_cloudwatch_log_group.backend", "module.ecs.aws_cloudwatch_log_group.ecs_exec", "module.ecs.aws_ecs_cluster.main", "module.ecs.aws_ecs_task_definition.backend", "module.ecs.aws_iam_role.ecs_task_role", "module.security_groups.aws_security_group.alb", "module.security_groups.aws_security_group.ecs_backend", "module.ssm.aws_iam_role.ecs_task_execution_role", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.main"]}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_ecs_service", "name": "frontend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"alarms": [], "availability_zone_rebalancing": "DISABLED", "capacity_provider_strategy": [], "cluster": "arn:aws:ecs:us-east-1:************:cluster/expendra-prod-cluster", "deployment_circuit_breaker": [{"enable": false, "rollback": false}], "deployment_controller": [{"type": "ECS"}], "deployment_maximum_percent": 200, "deployment_minimum_healthy_percent": 100, "desired_count": 1, "enable_ecs_managed_tags": false, "enable_execute_command": false, "force_delete": null, "force_new_deployment": null, "health_check_grace_period_seconds": 0, "iam_role": "/aws-service-role/ecs.amazonaws.com/AWSServiceRoleForECS", "id": "arn:aws:ecs:us-east-1:************:service/expendra-prod-cluster/expendra-prod-frontend", "launch_type": "FARGATE", "load_balancer": [{"container_name": "frontend", "container_port": 80, "elb_name": "", "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/expendra-prod-frontend-tg/dc55738fd212d397"}], "name": "expendra-prod-frontend", "network_configuration": [{"assign_public_ip": false, "security_groups": ["sg-0fcfd667a487ed27a"], "subnets": ["subnet-0102115d12ff8a351", "subnet-074fc3f2b9eb5c618", "subnet-0f0676440188ece3c"]}], "ordered_placement_strategy": [], "placement_constraints": [], "platform_version": "LATEST", "propagate_tags": "NONE", "scheduling_strategy": "REPLICA", "service_connect_configuration": [], "service_registries": [], "tags": {"Environment": "prod", "Name": "expendra-prod-frontend-service", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-frontend-service", "Project": "expendra"}, "task_definition": "arn:aws:ecs:us-east-1:************:task-definition/expendra-prod-frontend:1", "timeouts": null, "triggers": {}, "volume_configuration": [], "vpc_lattice_configurations": [], "wait_for_steady_state": false}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb_target_group.frontend", "module.ecr.aws_ecr_repository.frontend", "module.ecs.aws_cloudwatch_log_group.ecs_exec", "module.ecs.aws_cloudwatch_log_group.frontend", "module.ecs.aws_ecs_cluster.main", "module.ecs.aws_ecs_task_definition.frontend", "module.ecs.aws_iam_role.ecs_task_role", "module.security_groups.aws_security_group.alb", "module.security_groups.aws_security_group.ecs_frontend", "module.ssm.aws_iam_role.ecs_task_execution_role", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.main"]}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_ecs_task_definition", "name": "backend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:task-definition/expendra-prod-backend:1", "arn_without_revision": "arn:aws:ecs:us-east-1:************:task-definition/expendra-prod-backend", "container_definitions": "[{\"environment\":[{\"name\":\"AWS_REGION\",\"value\":\"us-east-1\"},{\"name\":\"ENVIRONMENT\",\"value\":\"prod\"}],\"essential\":true,\"healthCheck\":{\"command\":[\"CMD-SHELL\",\"curl -f http://localhost:8000/api/health || exit 1\"],\"interval\":30,\"retries\":3,\"startPeriod\":60,\"timeout\":5},\"image\":\"************.dkr.ecr.us-east-1.amazonaws.com/expendra-backend:latest\",\"logConfiguration\":{\"logDriver\":\"awslogs\",\"options\":{\"awslogs-group\":\"/aws/ecs/expendra-prod-backend\",\"awslogs-region\":\"us-east-1\",\"awslogs-stream-prefix\":\"ecs\"}},\"mountPoints\":[],\"name\":\"backend\",\"portMappings\":[{\"containerPort\":8000,\"hostPort\":8000,\"protocol\":\"tcp\"}],\"systemControls\":[],\"volumesFrom\":[]}]", "cpu": "512", "enable_fault_injection": false, "ephemeral_storage": [], "execution_role_arn": "arn:aws:iam::************:role/expendra-prod-ecs-task-execution-role", "family": "expendra-prod-backend", "id": "expendra-prod-backend", "inference_accelerator": [], "ipc_mode": "", "memory": "1024", "network_mode": "awsvpc", "pid_mode": "", "placement_constraints": [], "proxy_configuration": [], "requires_compatibilities": ["FARGATE"], "revision": 1, "runtime_platform": [], "skip_destroy": false, "tags": {"Environment": "prod", "Name": "expendra-prod-backend-task", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-backend-task", "Project": "expendra"}, "task_role_arn": "arn:aws:iam::************:role/expendra-prod-ecs-task-role", "track_latest": false, "volume": []}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["module.ecr.aws_ecr_repository.backend", "module.ecs.aws_cloudwatch_log_group.backend", "module.ecs.aws_iam_role.ecs_task_role", "module.ssm.aws_iam_role.ecs_task_execution_role"]}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_ecs_task_definition", "name": "frontend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:task-definition/expendra-prod-frontend:1", "arn_without_revision": "arn:aws:ecs:us-east-1:************:task-definition/expendra-prod-frontend", "container_definitions": "[{\"environment\":[{\"name\":\"ENVIRONMENT\",\"value\":\"prod\"}],\"essential\":true,\"healthCheck\":{\"command\":[\"CMD-SHELL\",\"curl -f http://localhost:80/ || exit 1\"],\"interval\":30,\"retries\":3,\"startPeriod\":30,\"timeout\":5},\"image\":\"************.dkr.ecr.us-east-1.amazonaws.com/expendra-frontend:latest\",\"logConfiguration\":{\"logDriver\":\"awslogs\",\"options\":{\"awslogs-region\":\"us-east-1\",\"awslogs-stream-prefix\":\"ecs\",\"awslogs-group\":\"/aws/ecs/expendra-prod-frontend\"}},\"mountPoints\":[],\"name\":\"frontend\",\"portMappings\":[{\"containerPort\":80,\"hostPort\":80,\"protocol\":\"tcp\"}],\"systemControls\":[],\"volumesFrom\":[]}]", "cpu": "256", "enable_fault_injection": false, "ephemeral_storage": [], "execution_role_arn": "arn:aws:iam::************:role/expendra-prod-ecs-task-execution-role", "family": "expendra-prod-frontend", "id": "expendra-prod-frontend", "inference_accelerator": [], "ipc_mode": "", "memory": "512", "network_mode": "awsvpc", "pid_mode": "", "placement_constraints": [], "proxy_configuration": [], "requires_compatibilities": ["FARGATE"], "revision": 1, "runtime_platform": [], "skip_destroy": false, "tags": {"Environment": "prod", "Name": "expendra-prod-frontend-task", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-frontend-task", "Project": "expendra"}, "task_role_arn": "arn:aws:iam::************:role/expendra-prod-ecs-task-role", "track_latest": false, "volume": []}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["module.ecr.aws_ecr_repository.frontend", "module.ecs.aws_cloudwatch_log_group.frontend", "module.ecs.aws_iam_role.ecs_task_role", "module.ssm.aws_iam_role.ecs_task_execution_role"]}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_iam_role", "name": "ecs_task_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/expendra-prod-ecs-task-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-29T17:13:47Z", "description": "", "force_detach_policies": false, "id": "expendra-prod-ecs-task-role", "inline_policy": [{"name": "expendra-prod-ecs-task-role-exec-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ssmmessages:CreateControlChannel\",\"ssmmessages:CreateDataChannel\",\"ssmmessages:OpenControlChannel\",\"ssmmessages:OpenDataChannel\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "expendra-prod-ecs-task-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "prod", "Name": "expendra-prod-ecs-task-role", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-ecs-task-role", "Project": "expendra"}, "unique_id": "AROATOAAUQWYMWYBWKBK3"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_iam_role_policy", "name": "ecs_task_role_exec_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "expendra-prod-ecs-task-role:expendra-prod-ecs-task-role-exec-policy", "name": "expendra-prod-ecs-task-role-exec-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ssmmessages:CreateControlChannel\",\"ssmmessages:CreateDataChannel\",\"ssmmessages:OpenControlChannel\",\"ssmmessages:OpenDataChannel\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "expendra-prod-ecs-task-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.ecs.aws_iam_role.ecs_task_role"]}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket", "name": "static_assets", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::expendra-prod-static-assets-do3sg8op", "bucket": "expendra-prod-static-assets-do3sg8op", "bucket_domain_name": "expendra-prod-static-assets-do3sg8op.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "expendra-prod-static-assets-do3sg8op.s3.us-east-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "786221b5ae0a64f027a5182ba616bbf290f017bfd89d5ad6aebd44078eb5fc9a", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "expendra-prod-static-assets-do3sg8op", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Condition\":{\"StringEquals\":{\"AWS:SourceArn\":\"arn:aws:cloudfront::************:distribution/E2XDJ0AMBOPN3U\"}},\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"cloudfront.amazonaws.com\"},\"Resource\":\"arn:aws:s3:::expendra-prod-static-assets-do3sg8op/*\",\"Sid\":\"AllowCloudFrontServicePrincipal\"}],\"Version\":\"2012-10-17\"}", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "prod", "Name": "expendra-prod-static-assets", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-static-assets", "Project": "expendra"}, "timeouts": null, "versioning": [{"enabled": true, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.s3.random_string.bucket_suffix"]}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "static_assets", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "expendra-prod-static-assets-do3sg8op", "id": "expendra-prod-static-assets-do3sg8op", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.s3.aws_s3_bucket.static_assets", "module.s3.random_string.bucket_suffix"]}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "static_assets", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "expendra-prod-static-assets-do3sg8op", "expected_bucket_owner": "", "id": "expendra-prod-static-assets-do3sg8op", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.s3.aws_s3_bucket.static_assets", "module.s3.random_string.bucket_suffix"]}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket_versioning", "name": "static_assets", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "expendra-prod-static-assets-do3sg8op", "expected_bucket_owner": "", "id": "expendra-prod-static-assets-do3sg8op", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.s3.aws_s3_bucket.static_assets", "module.s3.random_string.bucket_suffix"]}]}, {"module": "module.s3", "mode": "managed", "type": "random_string", "name": "bucket_suffix", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 2, "attributes": {"id": "do3sg8op", "keepers": null, "length": 8, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": null, "result": "do3sg8op", "special": false, "upper": false}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.security_groups", "mode": "managed", "type": "aws_security_group", "name": "alb", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0f22269bbe5e2e939", "description": "Managed by Terraform", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0f22269bbe5e2e939", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "HTTP", "from_port": 80, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 80}, {"cidr_blocks": ["0.0.0.0/0"], "description": "HTTPS", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 443}], "name": "expendra-prod-alb-20250629171408060200000003", "name_prefix": "expendra-prod-alb-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "Name": "expendra-prod-alb-sg", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-alb-sg", "Project": "expendra"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["module.vpc.aws_vpc.main"], "create_before_destroy": true}]}, {"module": "module.security_groups", "mode": "managed", "type": "aws_security_group", "name": "database", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0f34a629067e0bad2", "description": "Managed by Terraform", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0f34a629067e0bad2", "ingress": [{"cidr_blocks": [], "description": "PostgreSQL from ECS Backend", "from_port": 5432, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0bb654cbe7cab50ec"], "self": false, "to_port": 5432}], "name": "expendra-prod-database-20250629171415157100000007", "name_prefix": "expendra-prod-database-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "Name": "expendra-prod-database-sg", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-database-sg", "Project": "expendra"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["module.security_groups.aws_security_group.alb", "module.security_groups.aws_security_group.ecs_backend", "module.vpc.aws_vpc.main"], "create_before_destroy": true}]}, {"module": "module.security_groups", "mode": "managed", "type": "aws_security_group", "name": "ecs_backend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0bb654cbe7cab50ec", "description": "Managed by Terraform", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0bb654cbe7cab50ec", "ingress": [{"cidr_blocks": [], "description": "HTTP from ALB", "from_port": 8000, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0f22269bbe5e2e939"], "self": false, "to_port": 8000}], "name": "expendra-prod-ecs-backend-20250629171411838200000006", "name_prefix": "expendra-prod-ecs-backend-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "Name": "expendra-prod-ecs-backend-sg", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-ecs-backend-sg", "Project": "expendra"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["module.security_groups.aws_security_group.alb", "module.vpc.aws_vpc.main"], "create_before_destroy": true}]}, {"module": "module.security_groups", "mode": "managed", "type": "aws_security_group", "name": "ecs_frontend", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0fcfd667a487ed27a", "description": "Managed by Terraform", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0fcfd667a487ed27a", "ingress": [{"cidr_blocks": [], "description": "HTTP from ALB", "from_port": 80, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0f22269bbe5e2e939"], "self": false, "to_port": 80}], "name": "expendra-prod-ecs-frontend-20250629171411838200000005", "name_prefix": "expendra-prod-ecs-frontend-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "Name": "expendra-prod-ecs-frontend-sg", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-ecs-frontend-sg", "Project": "expendra"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["module.security_groups.aws_security_group.alb", "module.vpc.aws_vpc.main"], "create_before_destroy": true}]}, {"module": "module.ssm", "mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "US East (N. Virginia)", "endpoint": "ec2.us-east-1.amazonaws.com", "id": "us-east-1", "name": "us-east-1"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.ssm", "mode": "managed", "type": "aws_iam_role", "name": "ecs_task_execution_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/expendra-prod-ecs-task-execution-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-29T17:13:47Z", "description": "", "force_detach_policies": false, "id": "expendra-prod-ecs-task-execution-role", "inline_policy": [{"name": "expendra-prod-ecs-ssm-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ssm:GetParameter\",\"ssm:GetParameters\",\"ssm:GetParametersByPath\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:ssm:*:*:parameter/expendra-prod/*\"]},{\"Action\":[\"kms:Decrypt\"],\"Condition\":{\"StringEquals\":{\"kms:ViaService\":\"ssm.us-east-1.amazonaws.com\"}},\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"], "max_session_duration": 3600, "name": "expendra-prod-ecs-task-execution-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "expendra"}, "unique_id": "AROATOAAUQWYA6IWIUL5O"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"module": "module.ssm", "mode": "managed", "type": "aws_iam_role_policy", "name": "ecs_ssm_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "expendra-prod-ecs-task-execution-role:expendra-prod-ecs-ssm-policy", "name": "expendra-prod-ecs-ssm-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ssm:GetParameter\",\"ssm:GetParameters\",\"ssm:GetParametersByPath\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:ssm:*:*:parameter/expendra-prod/*\"]},{\"Action\":[\"kms:Decrypt\"],\"Condition\":{\"StringEquals\":{\"kms:ViaService\":\"ssm.us-east-1.amazonaws.com\"}},\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "expendra-prod-ecs-task-execution-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.ssm.aws_iam_role.ecs_task_execution_role", "module.ssm.data.aws_region.current"]}]}, {"module": "module.ssm", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecs_task_execution_role_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "expendra-prod-ecs-task-execution-role-20250629171356445900000002", "policy_arn": "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy", "role": "expendra-prod-ecs-task-execution-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.ssm.aws_iam_role.ecs_task_execution_role"]}]}, {"module": "module.ssm", "mode": "managed", "type": "aws_ssm_parameter", "name": "app_config", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "access-token-expire", "schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/app/config/access-token-expire", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/app/config/access-token-expire", "insecure_value": null, "key_id": "", "name": "/expendra-prod/app/config/access-token-expire", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-access-token-expire", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-access-token-expire", "Project": "expendra"}, "tier": "Standard", "type": "String", "value": "30", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA=="}, {"index_key": "cors-origins", "schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/app/config/cors-origins", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/app/config/cors-origins", "insecure_value": null, "key_id": "", "name": "/expendra-prod/app/config/cors-origins", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-cors-origins", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-cors-origins", "Project": "expendra"}, "tier": "Standard", "type": "String", "value": "*", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA=="}, {"index_key": "debug", "schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/app/config/debug", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/app/config/debug", "insecure_value": null, "key_id": "", "name": "/expendra-prod/app/config/debug", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-debug", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-debug", "Project": "expendra"}, "tier": "Standard", "type": "String", "value": "false", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA=="}, {"index_key": "log-level", "schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/app/config/log-level", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/app/config/log-level", "insecure_value": null, "key_id": "", "name": "/expendra-prod/app/config/log-level", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-log-level", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-log-level", "Project": "expendra"}, "tier": "Standard", "type": "String", "value": "info", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA=="}, {"index_key": "max-request-size", "schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/app/config/max-request-size", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/app/config/max-request-size", "insecure_value": null, "key_id": "", "name": "/expendra-prod/app/config/max-request-size", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-max-request-size", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-max-request-size", "Project": "expendra"}, "tier": "Standard", "type": "String", "value": "10485760", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA=="}, {"index_key": "rate-limit-requests", "schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/app/config/rate-limit-requests", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/app/config/rate-limit-requests", "insecure_value": null, "key_id": "", "name": "/expendra-prod/app/config/rate-limit-requests", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-rate-limit-requests", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-rate-limit-requests", "Project": "expendra"}, "tier": "Standard", "type": "String", "value": "100", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA=="}, {"index_key": "rate-limit-window", "schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/app/config/rate-limit-window", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/app/config/rate-limit-window", "insecure_value": null, "key_id": "", "name": "/expendra-prod/app/config/rate-limit-window", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-rate-limit-window", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-config-rate-limit-window", "Project": "expendra"}, "tier": "Standard", "type": "String", "value": "60", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"module": "module.ssm", "mode": "managed", "type": "aws_ssm_parameter", "name": "database_password", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/database/password", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/database/password", "insecure_value": null, "key_id": "alias/aws/ssm", "name": "/expendra-prod/database/password", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-database-password", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-database-password", "Project": "expendra"}, "tier": "Standard", "type": "SecureString", "value": "<3tQEucnl:q<nREL", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["random_password.db_password"]}]}, {"module": "module.ssm", "mode": "managed", "type": "aws_ssm_parameter", "name": "database_url", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/database/url", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/database/url", "insecure_value": null, "key_id": "alias/aws/ssm", "name": "/expendra-prod/database/url", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-database-url", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-database-url", "Project": "expendra"}, "tier": "Standard", "type": "SecureString", "value": "postgresql://expendra_user:<3tQEucnl:q<<EMAIL>:5432:5432/expendra", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.database.aws_db_instance.main", "module.database.aws_db_parameter_group.main", "module.database.aws_db_subnet_group.main", "module.database.aws_iam_role.rds_monitoring", "module.security_groups.aws_security_group.alb", "module.security_groups.aws_security_group.database", "module.security_groups.aws_security_group.ecs_backend", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.main", "random_password.db_password"]}]}, {"module": "module.ssm", "mode": "managed", "type": "aws_ssm_parameter", "name": "environment", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/app/environment", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/app/environment", "insecure_value": null, "key_id": "", "name": "/expendra-prod/app/environment", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-environment", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-environment", "Project": "expendra"}, "tier": "Standard", "type": "String", "value": "prod", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"module": "module.ssm", "mode": "managed", "type": "aws_ssm_parameter", "name": "secret_key", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/expendra-prod/app/secret-key", "data_type": "text", "description": "", "has_value_wo": null, "id": "/expendra-prod/app/secret-key", "insecure_value": null, "key_id": "alias/aws/ssm", "name": "/expendra-prod/app/secret-key", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-secret-key", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-secret-key", "Project": "expendra"}, "tier": "Standard", "type": "SecureString", "value": "z7!:C9_iaoTi6d>2:JaB6U*-cc3ts#jz%Vhx_iG4@X:zl[bxTLQgo#1Z?y#Z(NVH", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}], [{"type": "get_attr", "value": "value_wo"}]], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.ssm.random_password.secret_key"]}]}, {"module": "module.ssm", "mode": "managed", "type": "random_password", "name": "secret_key", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 3, "attributes": {"bcrypt_hash": "$2a$10$Dlim8QnTpHp6ounIGEcKH.SaJ.YcyRzIDsWpBy/aVtA6glnD7sft.", "id": "none", "keepers": null, "length": 64, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": null, "result": "z7!:C9_iaoTi6d>2:JaB6U*-cc3ts#jz%Vhx_iG4@X:zl[bxTLQgo#1Z?y#Z(NVH", "special": true, "upper": true}, "sensitive_attributes": [[{"type": "get_attr", "value": "bcrypt_hash"}], [{"type": "get_attr", "value": "result"}]], "identity_schema_version": 0}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_eip", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-0114c4f2e963494a6", "arn": "arn:aws:ec2:us-east-1:************:elastic-ip/eipalloc-0114c4f2e963494a6", "associate_with_private_ip": null, "association_id": "eipassoc-03ce299a8a33e3ec0", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-0114c4f2e963494a6", "instance": "", "ipam_pool_id": null, "network_border_group": "us-east-1", "network_interface": "eni-0e99c1984344f8b00", "private_dns": "ip-10-0-0-68.ec2.internal", "private_ip": "*********", "ptr_record": "", "public_dns": "ec2-44-209-186-249.compute-1.amazonaws.com", "public_ip": "**************", "public_ipv4_pool": "amazon", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-eip-1", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-eip-1", "Project": "expendra"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-04fbf9e53148d0961", "arn": "arn:aws:ec2:us-east-1:************:elastic-ip/eipalloc-04fbf9e53148d0961", "associate_with_private_ip": null, "association_id": "eipassoc-05db41d53dc267b4f", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-04fbf9e53148d0961", "instance": "", "ipam_pool_id": null, "network_border_group": "us-east-1", "network_interface": "eni-04ed3c48e7804a1f6", "private_dns": "ip-10-0-1-173.ec2.internal", "private_ip": "**********", "ptr_record": "", "public_dns": "ec2-98-85-199-218.compute-1.amazonaws.com", "public_ip": "*************", "public_ipv4_pool": "amazon", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-eip-2", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-eip-2", "Project": "expendra"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_vpc.main"]}, {"index_key": 2, "schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-0fa7753b9a52b27e1", "arn": "arn:aws:ec2:us-east-1:************:elastic-ip/eipalloc-0fa7753b9a52b27e1", "associate_with_private_ip": null, "association_id": "eipassoc-0402288caa9959186", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-0fa7753b9a52b27e1", "instance": "", "ipam_pool_id": null, "network_border_group": "us-east-1", "network_interface": "eni-03bb51136ba6ca7e1", "private_dns": "ip-10-0-2-143.ec2.internal", "private_ip": "**********", "ptr_record": "", "public_dns": "ec2-34-192-254-165.compute-1.amazonaws.com", "public_ip": "**************", "public_ipv4_pool": "amazon", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-eip-3", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-eip-3", "Project": "expendra"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_vpc.main"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_internet_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:internet-gateway/igw-04778cf73c593d3f2", "id": "igw-04778cf73c593d3f2", "owner_id": "************", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-igw", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-igw", "Project": "expendra"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.vpc.aws_vpc.main"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_nat_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"allocation_id": "eipalloc-0114c4f2e963494a6", "association_id": "eipassoc-03ce299a8a33e3ec0", "connectivity_type": "public", "id": "nat-093db68d2148c795f", "network_interface_id": "eni-0e99c1984344f8b00", "private_ip": "*********", "public_ip": "**************", "secondary_allocation_ids": [], "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-072c7f3424118b4ea", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-gateway-1", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-gateway-1", "Project": "expendra"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"allocation_id": "eipalloc-04fbf9e53148d0961", "association_id": "eipassoc-05db41d53dc267b4f", "connectivity_type": "public", "id": "nat-0e91b832db906cb62", "network_interface_id": "eni-04ed3c48e7804a1f6", "private_ip": "**********", "public_ip": "*************", "secondary_allocation_ids": [], "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-0d0d900cc223caf27", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-gateway-2", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-gateway-2", "Project": "expendra"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}, {"index_key": 2, "schema_version": 0, "attributes": {"allocation_id": "eipalloc-0fa7753b9a52b27e1", "association_id": "eipassoc-0402288caa9959186", "connectivity_type": "public", "id": "nat-004f3244a4cd56b6b", "network_interface_id": "eni-03bb51136ba6ca7e1", "private_ip": "**********", "public_ip": "**************", "secondary_allocation_ids": [], "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-03b6b3fd2924864cc", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-gateway-3", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-nat-gateway-3", "Project": "expendra"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-0f3deba50ec856f13", "id": "rtb-0f3deba50ec856f13", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-093db68d2148c795f", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-rt-1", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-rt-1", "Project": "expendra"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_nat_gateway.main", "module.vpc.aws_subnet.private", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-0967e6ec797d7e2ca", "id": "rtb-0967e6ec797d7e2ca", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-0e91b832db906cb62", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-rt-2", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-rt-2", "Project": "expendra"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_nat_gateway.main", "module.vpc.aws_subnet.private", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}, {"index_key": 2, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-0b4c801015f6bc7dd", "id": "rtb-0b4c801015f6bc7dd", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-004f3244a4cd56b6b", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-rt-3", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-rt-3", "Project": "expendra"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_nat_gateway.main", "module.vpc.aws_subnet.private", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-043d704999fde9dc1", "id": "rtb-043d704999fde9dc1", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-04778cf73c593d3f2", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-public-rt", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-public-rt", "Project": "expendra"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["module.vpc.aws_internet_gateway.main", "module.vpc.aws_vpc.main"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table_association", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0d54de67950119563", "route_table_id": "rtb-0f3deba50ec856f13", "subnet_id": "subnet-0102115d12ff8a351", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_nat_gateway.main", "module.vpc.aws_route_table.private", "module.vpc.aws_subnet.private", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-08e7516cb8f68c057", "route_table_id": "rtb-0967e6ec797d7e2ca", "subnet_id": "subnet-074fc3f2b9eb5c618", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_nat_gateway.main", "module.vpc.aws_route_table.private", "module.vpc.aws_subnet.private", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}, {"index_key": 2, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0daa2c7148a722714", "route_table_id": "rtb-0b4c801015f6bc7dd", "subnet_id": "subnet-0f0676440188ece3c", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_nat_gateway.main", "module.vpc.aws_route_table.private", "module.vpc.aws_subnet.private", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table_association", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0ac835f872a64dc21", "route_table_id": "rtb-043d704999fde9dc1", "subnet_id": "subnet-072c7f3424118b4ea", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_route_table.public", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-059865382b1ea14d7", "route_table_id": "rtb-043d704999fde9dc1", "subnet_id": "subnet-0d0d900cc223caf27", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_route_table.public", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}, {"index_key": 2, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0fdca5bdfdad95c96", "route_table_id": "rtb-043d704999fde9dc1", "subnet_id": "subnet-03b6b3fd2924864cc", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_internet_gateway.main", "module.vpc.aws_route_table.public", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.main"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_subnet", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0102115d12ff8a351", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az4", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0102115d12ff8a351", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-subnet-1", "Project": "expendra", "Type": "Private"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-subnet-1", "Project": "expendra", "Type": "Private"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.main"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-074fc3f2b9eb5c618", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az6", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-074fc3f2b9eb5c618", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-subnet-2", "Project": "expendra", "Type": "Private"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-subnet-2", "Project": "expendra", "Type": "Private"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.main"]}, {"index_key": 2, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0f0676440188ece3c", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1c", "availability_zone_id": "use1-az1", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0f0676440188ece3c", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-subnet-3", "Project": "expendra", "Type": "Private"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-private-subnet-3", "Project": "expendra", "Type": "Private"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.main"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_subnet", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-072c7f3424118b4ea", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az4", "cidr_block": "10.0.0.0/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-072c7f3424118b4ea", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-public-subnet-1", "Project": "expendra", "Type": "Public"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-public-subnet-1", "Project": "expendra", "Type": "Public"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.main"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0d0d900cc223caf27", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az6", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0d0d900cc223caf27", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-public-subnet-2", "Project": "expendra", "Type": "Public"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-public-subnet-2", "Project": "expendra", "Type": "Public"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.main"]}, {"index_key": 2, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-03b6b3fd2924864cc", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1c", "availability_zone_id": "use1-az1", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-03b6b3fd2924864cc", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-public-subnet-3", "Project": "expendra", "Type": "Public"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-public-subnet-3", "Project": "expendra", "Type": "Public"}, "timeouts": null, "vpc_id": "vpc-09b56c429ea6ab2aa"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.main"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc/vpc-09b56c429ea6ab2aa", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-0a888680ee8362364", "default_route_table_id": "rtb-0109a0cb8a69e1f7c", "default_security_group_id": "sg-0ad167bd811b5dfd6", "dhcp_options_id": "dopt-0b695b8f55ea43362", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-09b56c429ea6ab2aa", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0109a0cb8a69e1f7c", "owner_id": "************", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-vpc", "Project": "expendra"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "expendra-prod-vpc", "Project": "expendra"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "create_before_destroy": true}]}], "check_results": null}