#!/bin/bash
# Expendra CI/CD Deployment Script
# This script is designed to run in GitHub Actions or other CI/CD environments

set -e  # Exit on any error

# Configuration
PROJECT_NAME="expendra"
AWS_REGION="${AWS_REGION:-us-east-1}"
ENVIRONMENT="${ENVIRONMENT:-prod}"
IMAGE_TAG="${GITHUB_SHA:-latest}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI not found"
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker not found"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials not configured"
        exit 1
    fi
    
    # Get AWS account ID
    AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    export AWS_ACCOUNT_ID
    
    log_success "Prerequisites check passed"
}

# Function to build and push Docker images
build_and_push() {
    log_info "Building and pushing Docker images..."
    
    # Login to ECR
    log_info "Logging into ECR..."
    aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
    
    # Build backend image
    log_info "Building backend image..."
    cd backend
    docker build -t $PROJECT_NAME-backend:$IMAGE_TAG .
    docker tag $PROJECT_NAME-backend:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:$IMAGE_TAG
    docker tag $PROJECT_NAME-backend:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:latest
    
    # Push backend image
    log_info "Pushing backend image..."
    docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:$IMAGE_TAG
    docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:latest
    cd ..
    
    # Build frontend image
    log_info "Building frontend image..."
    cd frontend
    docker build -t $PROJECT_NAME-frontend:$IMAGE_TAG .
    docker tag $PROJECT_NAME-frontend:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:$IMAGE_TAG
    docker tag $PROJECT_NAME-frontend:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:latest
    
    # Push frontend image
    log_info "Pushing frontend image..."
    docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:$IMAGE_TAG
    docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:latest
    cd ..
    
    log_success "Docker images built and pushed successfully"
}

# Function to deploy to ECS
deploy_to_ecs() {
    log_info "Deploying to ECS..."
    
    # Determine cluster and service names based on environment
    if [ "$ENVIRONMENT" = "staging" ]; then
        CLUSTER_NAME="$PROJECT_NAME-cluster-staging"
        BACKEND_SERVICE="$PROJECT_NAME-backend-service-staging"
        FRONTEND_SERVICE="$PROJECT_NAME-frontend-service-staging"
    else
        CLUSTER_NAME="$PROJECT_NAME-cluster"
        BACKEND_SERVICE="$PROJECT_NAME-backend-service"
        FRONTEND_SERVICE="$PROJECT_NAME-frontend-service"
    fi
    
    # Update backend service
    log_info "Updating backend service..."
    aws ecs update-service \
        --cluster $CLUSTER_NAME \
        --service $BACKEND_SERVICE \
        --force-new-deployment \
        --region $AWS_REGION
    
    # Update frontend service
    log_info "Updating frontend service..."
    aws ecs update-service \
        --cluster $CLUSTER_NAME \
        --service $FRONTEND_SERVICE \
        --force-new-deployment \
        --region $AWS_REGION
    
    # Wait for services to stabilize
    log_info "Waiting for services to stabilize..."
    aws ecs wait services-stable \
        --cluster $CLUSTER_NAME \
        --services $BACKEND_SERVICE $FRONTEND_SERVICE \
        --region $AWS_REGION
    
    log_success "Deployment completed successfully"
}

# Function to check deployment status
check_status() {
    log_info "Checking deployment status..."
    
    # Determine cluster name based on environment
    if [ "$ENVIRONMENT" = "staging" ]; then
        CLUSTER_NAME="$PROJECT_NAME-cluster-staging"
    else
        CLUSTER_NAME="$PROJECT_NAME-cluster"
    fi
    
    # Get service status
    aws ecs describe-services \
        --cluster $CLUSTER_NAME \
        --region $AWS_REGION \
        --query "services[*].[serviceName,status,runningCount,desiredCount]" \
        --output table
}

# Function to run database migrations (if needed)
run_migrations() {
    log_info "Running database migrations..."
    # This would typically run database migrations
    # For now, we'll just log that this step would happen
    log_info "Database migrations would run here"
}

# Function to run health checks
health_check() {
    log_info "Running health checks..."
    
    # Get the application URL from Terraform outputs
    if [ -f "infrastructure/terraform.tfstate" ]; then
        APP_URL=$(cd infrastructure && terraform output -raw application_url 2>/dev/null || echo "")
        if [ -n "$APP_URL" ]; then
            log_info "Testing application health at $APP_URL"
            
            # Wait a bit for the application to start
            sleep 30
            
            # Test backend health
            if curl -f "$APP_URL/api/health" &> /dev/null; then
                log_success "Backend health check passed"
            else
                log_warning "Backend health check failed"
            fi
            
            # Test frontend
            if curl -f "$APP_URL" &> /dev/null; then
                log_success "Frontend health check passed"
            else
                log_warning "Frontend health check failed"
            fi
        fi
    fi
}

# Main deployment function
main() {
    log_info "Starting deployment for environment: $ENVIRONMENT"
    log_info "Image tag: $IMAGE_TAG"
    log_info "AWS Region: $AWS_REGION"
    
    check_prerequisites
    build_and_push
    deploy_to_ecs
    run_migrations
    health_check
    check_status
    
    log_success "Deployment pipeline completed successfully!"
}

# Handle script arguments
case "${1:-deploy}" in
    "build")
        check_prerequisites
        build_and_push
        ;;
    "deploy")
        main
        ;;
    "status")
        check_prerequisites
        check_status
        ;;
    "health")
        health_check
        ;;
    *)
        echo "Usage: $0 {build|deploy|status|health}"
        echo "  build  - Build and push Docker images only"
        echo "  deploy - Full deployment pipeline (default)"
        echo "  status - Check deployment status"
        echo "  health - Run health checks"
        exit 1
        ;;
esac
