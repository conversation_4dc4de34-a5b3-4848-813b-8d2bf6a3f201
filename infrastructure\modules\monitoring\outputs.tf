output "dashboard_url" {
  description = "CloudWatch dashboard URL"
  value       = "https://${var.aws_region}.console.aws.amazon.com/cloudwatch/home?region=${var.aws_region}#dashboards:name=${aws_cloudwatch_dashboard.main.dashboard_name}"
}

output "backend_log_group_name" {
  description = "Backend log group name"
  value       = aws_cloudwatch_log_group.backend.name
}

output "frontend_log_group_name" {
  description = "Frontend log group name"
  value       = aws_cloudwatch_log_group.frontend.name
}

output "database_log_group_name" {
  description = "Database log group name"
  value       = aws_cloudwatch_log_group.database.name
}

output "sns_topic_arn" {
  description = "SNS topic ARN for alerts"
  value       = var.alarm_notifications_enabled ? aws_sns_topic.alerts[0].arn : ""
}
