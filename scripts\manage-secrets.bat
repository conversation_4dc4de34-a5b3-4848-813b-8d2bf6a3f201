@echo off
REM Expendra Secrets Management Script
REM Usage: manage-secrets.bat [command] [environment]

if "%1"=="" goto :help
if "%1"=="help" goto :help
if "%1"=="set" goto :set
if "%1"=="get" goto :get
if "%1"=="list" goto :list
if "%1"=="delete" goto :delete
goto :help

:help
echo.
echo Expendra Secrets Management Script
echo ==================================
echo.
echo Available commands:
echo   set        - Set a secret parameter
echo   get        - Get a secret parameter
echo   list       - List all parameters
echo   delete     - Delete a parameter
echo   help       - Show this help message
echo.
echo Usage: manage-secrets.bat [command] [environment]
echo Environment defaults to 'prod' if not specified
echo.
echo Examples:
echo   manage-secrets.bat set prod
echo   manage-secrets.bat get prod
echo   manage-secrets.bat list prod
echo.
goto :end

:set
set ENV=%2
if "%ENV%"=="" set ENV=prod
echo Setting secrets for environment: %ENV%
echo.
echo Available secrets to set:
echo 1. OpenAI API Key
echo 2. Anthropic API Key
echo 3. Google API Key
echo 4. Application Secret Key
echo 5. Custom parameter
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto :set_openai
if "%choice%"=="2" goto :set_anthropic
if "%choice%"=="3" goto :set_google
if "%choice%"=="4" goto :set_secret_key
if "%choice%"=="5" goto :set_custom
echo Invalid choice.
goto :end

:set_openai
set /p api_key="Enter OpenAI API Key: "
aws ssm put-parameter --name "/expendra-%ENV%/api-keys/openai" --value "%api_key%" --type "SecureString" --overwrite
echo OpenAI API Key set successfully.
goto :end

:set_anthropic
set /p api_key="Enter Anthropic API Key: "
aws ssm put-parameter --name "/expendra-%ENV%/api-keys/anthropic" --value "%api_key%" --type "SecureString" --overwrite
echo Anthropic API Key set successfully.
goto :end

:set_google
set /p api_key="Enter Google API Key: "
aws ssm put-parameter --name "/expendra-%ENV%/api-keys/google" --value "%api_key%" --type "SecureString" --overwrite
echo Google API Key set successfully.
goto :end

:set_secret_key
set /p secret_key="Enter Application Secret Key: "
aws ssm put-parameter --name "/expendra-%ENV%/app/secret-key" --value "%secret_key%" --type "SecureString" --overwrite
echo Application Secret Key set successfully.
goto :end

:set_custom
set /p param_name="Enter parameter name (without prefix): "
set /p param_value="Enter parameter value: "
set /p param_type="Enter parameter type (String/SecureString): "
aws ssm put-parameter --name "/expendra-%ENV%/%param_name%" --value "%param_value%" --type "%param_type%" --overwrite
echo Parameter set successfully.
goto :end

:get
set ENV=%2
if "%ENV%"=="" set ENV=prod
set /p param_name="Enter parameter name (without prefix): "
echo Getting parameter: /expendra-%ENV%/%param_name%
aws ssm get-parameter --name "/expendra-%ENV%/%param_name%" --with-decryption --query "Parameter.Value" --output text
goto :end

:list
set ENV=%2
if "%ENV%"=="" set ENV=prod
echo Listing all parameters for environment: %ENV%
aws ssm get-parameters-by-path --path "/expendra-%ENV%" --recursive --query "Parameters[*].[Name,Type]" --output table
goto :end

:delete
set ENV=%2
if "%ENV%"=="" set ENV=prod
set /p param_name="Enter parameter name (without prefix): "
echo WARNING: This will delete parameter: /expendra-%ENV%/%param_name%
set /p confirm="Are you sure? (y/N): "
if /i "%confirm%"=="y" (
    aws ssm delete-parameter --name "/expendra-%ENV%/%param_name%"
    echo Parameter deleted successfully.
) else (
    echo Deletion cancelled.
)
goto :end

:end
