# Expendra Environment Configuration
# This file defines configuration for different deployment environments

# Development Environment
development:
  aws_region: "us-east-1"
  environment: "dev"
  
  # Infrastructure sizing (smaller for dev)
  database:
    instance_class: "db.t3.micro"
    allocated_storage: 20
    backup_retention: 1
    multi_az: false
  
  ecs:
    backend:
      cpu: 256
      memory: 512
      desired_count: 1
      min_capacity: 1
      max_capacity: 2
    frontend:
      cpu: 256
      memory: 512
      desired_count: 1
      min_capacity: 1
      max_capacity: 2
  
  # Application settings
  app:
    debug: true
    log_level: "debug"
    cors_origins: "*"
    rate_limit_requests: 1000
    rate_limit_window: 60
  
  # Monitoring
  monitoring:
    detailed_monitoring: false
    log_retention_days: 7
    alarm_notifications: false

# Staging Environment
staging:
  aws_region: "us-east-1"
  environment: "staging"
  
  # Infrastructure sizing (medium for staging)
  database:
    instance_class: "db.t3.small"
    allocated_storage: 50
    backup_retention: 7
    multi_az: false
  
  ecs:
    backend:
      cpu: 512
      memory: 1024
      desired_count: 2
      min_capacity: 1
      max_capacity: 4
    frontend:
      cpu: 256
      memory: 512
      desired_count: 2
      min_capacity: 1
      max_capacity: 4
  
  # Application settings
  app:
    debug: false
    log_level: "info"
    cors_origins: "https://staging.expendra.com"
    rate_limit_requests: 500
    rate_limit_window: 60
  
  # Monitoring
  monitoring:
    detailed_monitoring: true
    log_retention_days: 14
    alarm_notifications: true

# Production Environment
production:
  aws_region: "us-east-1"
  environment: "prod"
  
  # Infrastructure sizing (full for production)
  database:
    instance_class: "db.t3.medium"
    allocated_storage: 100
    backup_retention: 30
    multi_az: true
    encryption: true
  
  ecs:
    backend:
      cpu: 1024
      memory: 2048
      desired_count: 3
      min_capacity: 2
      max_capacity: 10
    frontend:
      cpu: 512
      memory: 1024
      desired_count: 3
      min_capacity: 2
      max_capacity: 10
  
  # Application settings
  app:
    debug: false
    log_level: "warning"
    cors_origins: "https://expendra.com"
    rate_limit_requests: 100
    rate_limit_window: 60
  
  # Monitoring
  monitoring:
    detailed_monitoring: true
    log_retention_days: 90
    alarm_notifications: true
    
  # Security
  security:
    waf_enabled: true
    ssl_policy: "ELBSecurityPolicy-TLS-1-2-2017-01"
    force_https: true

# Common settings across all environments
common:
  project_name: "expendra"
  
  # VPC Configuration
  vpc:
    cidr: "10.0.0.0/16"
    enable_dns_hostnames: true
    enable_dns_support: true
  
  # Default tags
  tags:
    Project: "Expendra"
    ManagedBy: "Terraform"
    Owner: "DevOps Team"
  
  # ECR Configuration
  ecr:
    image_tag_mutability: "MUTABLE"
    scan_on_push: true
    lifecycle_policy_days: 30
  
  # CloudWatch Configuration
  cloudwatch:
    log_groups:
      - "/aws/ecs/expendra-backend"
      - "/aws/ecs/expendra-frontend"
      - "/aws/rds/expendra"
    
  # S3 Configuration
  s3:
    versioning: true
    encryption: true
    public_access_block: true
  
  # Security Groups
  security_groups:
    backend:
      ingress:
        - port: 8000
          protocol: "tcp"
          source: "alb"
        - port: 443
          protocol: "tcp"
          source: "0.0.0.0/0"
    frontend:
      ingress:
        - port: 80
          protocol: "tcp"
          source: "alb"
        - port: 443
          protocol: "tcp"
          source: "0.0.0.0/0"
    database:
      ingress:
        - port: 5432
          protocol: "tcp"
          source: "backend"
