# Expendra CI/CD Pipeline Testing Guide

This guide provides comprehensive instructions for testing and validating the complete CI/CD pipeline for the Expendra application.

## Prerequisites

Before testing the pipeline, ensure you have:

1. **AWS Account Setup**
   - AWS CLI configured with appropriate permissions
   - AWS account with sufficient permissions for ECS, RDS, S3, CloudFront, etc.
   - Domain name (optional, for custom domain setup)

2. **Local Development Environment**
   - Docker and Docker Compose installed
   - Terraform installed (version 1.0+)
   - Node.js and npm/yarn for frontend development
   - Python 3.11+ for backend development

3. **GitHub Repository**
   - Repository with all CI/CD files committed
   - GitHub Actions enabled
   - Required secrets configured (see below)

## GitHub Secrets Configuration

Configure the following secrets in your GitHub repository:

### Required Secrets
```
AWS_ACCESS_KEY_ID          # AWS access key for deployment
AWS_SECRET_ACCESS_KEY      # AWS secret key for deployment
AWS_REGION                 # AWS region (e.g., us-east-1)
```

### Optional Secrets (for production)
```
OPENAI_API_KEY            # OpenAI API key for AI features
ANTHROPIC_API_KEY         # Anthropic API key for AI features
GOOGLE_API_KEY            # Google API key for search features
DATABASE_PASSWORD         # Custom database password
SECRET_KEY                # Application secret key
```

## Testing Phases

### Phase 1: Local Development Testing

#### 1.1 Test Docker Containers Locally

```bash
# Test backend container
cd backend
docker build -t expendra-backend:test .
docker run -p 8000:8000 expendra-backend:test

# Test frontend container
cd ../frontend
docker build -t expendra-frontend:test .
docker run -p 3000:80 expendra-frontend:test

# Test full stack with docker-compose
cd ..
docker-compose up --build
```

**Expected Results:**
- Backend accessible at http://localhost:8000
- Frontend accessible at http://localhost:3000
- API documentation at http://localhost:8000/docs
- Health check endpoint returns 200: http://localhost:8000/api/health

#### 1.2 Test Environment Configuration

```bash
# Test AWS configuration loading
cd backend
python -c "from config.aws_config import get_aws_config; print('AWS config loaded successfully')"

# Test logging configuration
python -c "from config.logging_config import get_logger; logger = get_logger('test'); logger.info('Test log message')"
```

### Phase 2: Infrastructure Testing

#### 2.1 Initialize Terraform

```bash
cd infrastructure
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your specific values

# Initialize Terraform
terraform init

# Validate configuration
terraform validate

# Plan deployment
terraform plan
```

**Expected Results:**
- Terraform initializes without errors
- Plan shows resources to be created
- No validation errors

#### 2.2 Deploy Infrastructure

```bash
# Apply infrastructure (use with caution)
terraform apply

# Verify outputs
terraform output
```

**Expected Results:**
- All resources created successfully
- Outputs show application URLs and resource ARNs
- No errors in Terraform apply

#### 2.3 Test Infrastructure Components

```bash
# Test VPC and networking
aws ec2 describe-vpcs --filters "Name=tag:Name,Values=expendra-*"

# Test ECS cluster
aws ecs describe-clusters --clusters expendra-cluster

# Test RDS instance
aws rds describe-db-instances --db-instance-identifier expendra-database

# Test ECR repositories
aws ecr describe-repositories --repository-names expendra-backend expendra-frontend
```

### Phase 3: CI/CD Pipeline Testing

#### 3.1 Test GitHub Actions Workflows

1. **Push to Feature Branch**
   ```bash
   git checkout -b test-pipeline
   echo "# Test change" >> README.md
   git add README.md
   git commit -m "Test CI/CD pipeline"
   git push origin test-pipeline
   ```

   **Expected Results:**
   - CI workflow runs automatically
   - Tests pass for both frontend and backend
   - Security scans complete without critical issues
   - Docker images build successfully

2. **Create Pull Request**
   - Create PR from test-pipeline to main
   - Verify all checks pass
   - Merge PR

   **Expected Results:**
   - All PR checks pass
   - Code quality gates satisfied
   - Security scans pass

3. **Test Production Deployment**
   ```bash
   git checkout main
   git pull origin main
   git tag v1.0.0-test
   git push origin v1.0.0-test
   ```

   **Expected Results:**
   - Production deployment workflow triggers
   - Docker images pushed to ECR
   - ECS services updated
   - Application accessible via ALB

### Phase 4: Application Testing

#### 4.1 Health Checks

```bash
# Use the monitoring script
scripts/monitor.bat health prod

# Or manual testing
curl -f https://your-app-url/api/health
curl -f https://your-app-url/
```

#### 4.2 API Testing

```bash
# Test API endpoints
curl -X GET https://your-app-url/api/v1/ \
  -H "Content-Type: application/json"

# Test authentication (if implemented)
curl -X POST https://your-app-url/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "test", "password": "test"}'
```

#### 4.3 Database Connectivity

```bash
# Check database connection through API
curl -X GET https://your-app-url/api/v1/health/database

# Check database logs
aws logs tail /aws/rds/instance/expendra-database/postgresql --since 1h
```

### Phase 5: Monitoring and Logging Testing

#### 5.1 CloudWatch Logs

```bash
# Check application logs
aws logs describe-log-groups --log-group-name-prefix "/aws/ecs/expendra"

# View recent logs
scripts/monitor.bat logs prod
```

#### 5.2 CloudWatch Metrics

```bash
# Check metrics
scripts/monitor.bat metrics prod

# View dashboard
scripts/monitor.bat dashboard prod
```

#### 5.3 Alarms Testing

```bash
# Check alarm status
scripts/monitor.bat alarms prod

# Test alarm triggers (optional - causes downtime)
# aws ecs update-service --cluster expendra-cluster --service expendra-backend-service --desired-count 0
```

## Validation Checklist

### Infrastructure Validation
- [ ] VPC created with public and private subnets
- [ ] ECS cluster running with services
- [ ] RDS database accessible from ECS tasks
- [ ] ALB routing traffic correctly
- [ ] S3 buckets created with proper permissions
- [ ] CloudFront distribution serving frontend
- [ ] ECR repositories created and accessible

### Application Validation
- [ ] Backend API responding to health checks
- [ ] Frontend loading correctly
- [ ] Database connections working
- [ ] Environment variables loaded from SSM
- [ ] Logging working and visible in CloudWatch
- [ ] CORS configured correctly for frontend-backend communication

### CI/CD Validation
- [ ] GitHub Actions workflows running successfully
- [ ] Docker images building and pushing to ECR
- [ ] ECS services updating with new deployments
- [ ] Security scans passing
- [ ] Tests running and passing
- [ ] Deployment notifications working (if configured)

### Security Validation
- [ ] SSL/TLS certificates working
- [ ] Security groups properly configured
- [ ] IAM roles following least privilege
- [ ] Secrets stored securely in SSM Parameter Store
- [ ] No hardcoded credentials in code
- [ ] Container images scanned for vulnerabilities

### Performance Validation
- [ ] Application response times acceptable
- [ ] Database performance adequate
- [ ] Auto-scaling configured and working
- [ ] Resource utilization within expected ranges
- [ ] CDN caching working for static assets

## Troubleshooting Common Issues

### 1. ECS Tasks Not Starting
```bash
# Check service events
aws ecs describe-services --cluster expendra-cluster --services expendra-backend-service

# Check task definition
aws ecs describe-task-definition --task-definition expendra-backend

# Check logs
aws logs tail /aws/ecs/expendra-backend --since 30m
```

### 2. Database Connection Issues
```bash
# Check security groups
aws ec2 describe-security-groups --filters "Name=group-name,Values=expendra-*"

# Check RDS status
aws rds describe-db-instances --db-instance-identifier expendra-database

# Test connectivity from ECS task
aws ecs execute-command --cluster expendra-cluster --task TASK_ID --container backend --interactive --command "/bin/bash"
```

### 3. GitHub Actions Failures
- Check GitHub Actions logs in the repository
- Verify AWS credentials are correctly configured
- Ensure all required secrets are set
- Check for resource limits or permissions issues

### 4. Terraform Issues
```bash
# Check Terraform state
terraform show

# Refresh state
terraform refresh

# Import existing resources if needed
terraform import aws_instance.example i-1234567890abcdef0
```

## Rollback Procedures

### Application Rollback
```bash
# Rollback to previous task definition
aws ecs update-service --cluster expendra-cluster --service expendra-backend-service --task-definition expendra-backend:PREVIOUS_REVISION

# Or use deployment script
scripts/deploy.bat rollback prod
```

### Infrastructure Rollback
```bash
# Destroy infrastructure (use with extreme caution)
cd infrastructure
terraform destroy

# Or rollback specific resources
terraform apply -target=aws_ecs_service.backend
```

## Performance Testing

### Load Testing
```bash
# Install Apache Bench or similar tool
# Test API endpoints
ab -n 1000 -c 10 https://your-app-url/api/health

# Test frontend
ab -n 1000 -c 10 https://your-app-url/
```

### Database Performance
```bash
# Monitor RDS performance metrics
aws cloudwatch get-metric-statistics --namespace AWS/RDS --metric-name CPUUtilization --dimensions Name=DBInstanceIdentifier,Value=expendra-database --start-time 2023-01-01T00:00:00Z --end-time 2023-01-01T01:00:00Z --period 300 --statistics Average
```

## Conclusion

This testing guide ensures that your Expendra CI/CD pipeline is working correctly and your application is deployed securely and efficiently. Regular testing of these components will help maintain a reliable deployment process.

For additional support, refer to the AWS documentation and the project's README files.
