# Expendra AWS Infrastructure
# This Terraform configuration sets up the complete AWS infrastructure for Expendra

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
  
  # Backend configuration for state management (commented out for initial deployment)
  # backend "s3" {
  #   bucket         = "expendra-terraform-state"
  #   key            = "expendra/terraform.tfstate"
  #   region         = "us-east-1"
  #   encrypt        = true
  #   dynamodb_table = "expendra-terraform-locks"
  # }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "Expendra"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

data "aws_caller_identity" "current" {}

# Local values
locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "Terraform"
  }
}

# VPC and Networking
module "vpc" {
  source = "./modules/vpc"

  name_prefix        = local.name_prefix
  vpc_cidr          = var.vpc_cidr
  availability_zones = data.aws_availability_zones.available.names
  tags              = local.common_tags
}

# Security Groups
module "security_groups" {
  source = "./modules/security"

  project_name = var.project_name
  environment  = var.environment
  vpc_id       = module.vpc.vpc_id
}

# RDS Database
module "database" {
  source = "./modules/rds"

  project_name                = var.project_name
  environment                 = var.environment
  private_subnet_ids          = module.vpc.private_subnet_ids
  database_security_group_id  = module.security_groups.database_security_group_id

  db_instance_class    = var.db_instance_class
  db_allocated_storage = var.db_allocated_storage
  db_name             = var.db_name
  db_username         = var.db_username
  db_password         = random_password.db_password.result
}

# Generate random password for database
resource "random_password" "db_password" {
  length  = 16
  special = true
}

# ECR Repositories
module "ecr" {
  source = "./modules/ecr"

  project_name = var.project_name
  environment  = var.environment
}

# ECS Cluster
module "ecs" {
  source = "./modules/ecs"

  project_name    = var.project_name
  environment     = var.environment
  aws_region      = var.aws_region
  aws_account_id  = data.aws_caller_identity.current.account_id

  backend_cpu     = var.backend_cpu
  backend_memory  = var.backend_memory
  frontend_cpu    = var.frontend_cpu
  frontend_memory = var.frontend_memory

  backend_image_uri  = module.ecr.backend_repository_url
  frontend_image_uri = module.ecr.frontend_repository_url

  ecs_task_execution_role_arn = module.ssm.ecs_task_execution_role_arn

  # Service configuration
  backend_desired_count  = var.backend_desired_count
  frontend_desired_count = var.frontend_desired_count

  # Networking
  private_subnet_ids = module.vpc.private_subnet_ids

  # Security Groups
  backend_security_group_id  = module.security_groups.ecs_backend_security_group_id
  frontend_security_group_id = module.security_groups.ecs_frontend_security_group_id

  # Load Balancer Target Groups
  backend_target_group_arn  = module.alb.backend_target_group_arn
  frontend_target_group_arn = module.alb.frontend_target_group_arn
}

# Application Load Balancer
module "alb" {
  source = "./modules/alb"

  project_name          = var.project_name
  environment           = var.environment
  vpc_id                = module.vpc.vpc_id
  public_subnet_ids     = module.vpc.public_subnet_ids
  alb_security_group_id = module.security_groups.alb_security_group_id
}

# S3 Buckets
module "s3" {
  source = "./modules/s3"

  project_name = var.project_name
  environment  = var.environment
}

# CloudFront Distribution
module "cloudfront" {
  source = "./modules/cloudfront"

  project_name           = var.project_name
  environment            = var.environment
  s3_bucket_id           = module.s3.static_assets_bucket_id
  s3_bucket_domain_name  = module.s3.static_assets_bucket_domain_name
  alb_dns_name           = module.alb.alb_dns_name
}

# S3 bucket policy for CloudFront access (created after CloudFront distribution)
resource "aws_s3_bucket_policy" "static_assets_cloudfront" {
  bucket = module.s3.static_assets_bucket_id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowCloudFrontServicePrincipal"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${module.s3.static_assets_bucket_arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = module.cloudfront.distribution_arn
          }
        }
      }
    ]
  })

  depends_on = [module.cloudfront, module.s3]
}

# Systems Manager Parameters (for secrets)
module "ssm" {
  source = "./modules/ssm"

  name_prefix = local.name_prefix
  environment = var.environment

  database_url      = "postgresql://${var.db_username}:${random_password.db_password.result}@${module.database.db_instance_endpoint}:${module.database.db_port}/${module.database.db_name}"
  database_password = random_password.db_password.result

  secret_key        = var.secret_key
  openai_api_key    = var.openai_api_key
  anthropic_api_key = var.anthropic_api_key
  google_api_key    = var.google_api_key

  tags = local.common_tags
}
